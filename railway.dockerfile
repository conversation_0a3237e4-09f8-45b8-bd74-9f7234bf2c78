# Use Node.js 18 - lightweight image for image-based PDF generation
FROM node:18-slim

# Install system dependencies for canvas package and PDF generation
RUN apt-get update && apt-get install -y \
    # Build tools needed for canvas compilation
    build-essential \
    pkg-config \
    python3 \
    # Canvas dependencies
    libcairo2-dev \
    libpango1.0-dev \
    libjpeg-dev \
    libgif-dev \
    librsvg2-dev \
    libpixman-1-dev \
    # Font support for PDFKit
    libfontconfig1 \
    # Cleanup to reduce image size
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install Node.js dependencies (production only)
RUN npm ci --only=production

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p public/templates generated

# Set environment variables
ENV NODE_ENV=production
ENV PORT=3000

# Expose port
EXPOSE 3000

# Start the application
CMD ["node", "server.js"]
