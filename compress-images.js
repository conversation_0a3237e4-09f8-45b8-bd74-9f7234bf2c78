const { createCanvas, loadImage } = require('canvas');
const fs = require('fs');
const path = require('path');

// Configuration for image compression - high quality for 3MB total target
const COMPRESSION_CONFIG = {
    quality: 0.85, // 85% quality for high visual quality (25% increase from 75%)
    maxWidth: 1400, // Even higher resolution for crisp text and details
    maxHeight: 1980 // Even higher resolution (maintains A4 ratio)
};

/**
 * Compress a single image file
 * @param {string} inputPath - Path to input image
 * @param {string} outputPath - Path to output compressed image
 */
async function compressImage(inputPath, outputPath) {
    try {
        console.log(`Compressing: ${inputPath}`);
        
        // Load the original image
        const image = await loadImage(inputPath);
        console.log(`Original dimensions: ${image.width}x${image.height}`);
        
        // Create canvas with reduced dimensions
        const canvas = createCanvas(COMPRESSION_CONFIG.maxWidth, COMPRESSION_CONFIG.maxHeight);
        const ctx = canvas.getContext('2d');
        
        // Draw the image scaled down to the canvas
        ctx.drawImage(image, 0, 0, COMPRESSION_CONFIG.maxWidth, COMPRESSION_CONFIG.maxHeight);
        
        // Convert to JPEG with compression
        const buffer = canvas.toBuffer('image/jpeg', { quality: COMPRESSION_CONFIG.quality });
        
        // Write the compressed image
        fs.writeFileSync(outputPath, buffer);
        
        // Log compression results
        const originalSize = fs.statSync(inputPath).size;
        const compressedSize = buffer.length;
        const compressionRatio = ((1 - compressedSize / originalSize) * 100).toFixed(1);
        
        console.log(`✅ Compressed: ${path.basename(inputPath)}`);
        console.log(`   Original: ${(originalSize / 1024).toFixed(1)}KB`);
        console.log(`   Compressed: ${(compressedSize / 1024).toFixed(1)}KB`);
        console.log(`   Reduction: ${compressionRatio}%`);
        console.log('');
        
        return { originalSize, compressedSize, compressionRatio };
    } catch (error) {
        console.error(`❌ Error compressing ${inputPath}:`, error.message);
        return null;
    }
}

/**
 * Compress all template images
 */
async function compressAllTemplateImages() {
    console.log('🔄 Starting image compression...\n');
    
    const templatesDir = path.join(__dirname, 'public', 'templates');
    const compressedDir = path.join(templatesDir, 'compressed');
    
    // Create compressed directory if it doesn't exist
    if (!fs.existsSync(compressedDir)) {
        fs.mkdirSync(compressedDir, { recursive: true });
        console.log(`📁 Created directory: ${compressedDir}\n`);
    }
    
    // List of images to compress
    const imagesToCompress = [
        'page1.jpg',
        'page2.jpg', 
        'page3.jpg',
        'page4.jpg'
    ];
    
    let totalOriginalSize = 0;
    let totalCompressedSize = 0;
    let successCount = 0;
    
    // Compress each image
    for (const imageFile of imagesToCompress) {
        const inputPath = path.join(templatesDir, imageFile);
        const outputPath = path.join(compressedDir, imageFile);
        
        if (fs.existsSync(inputPath)) {
            const result = await compressImage(inputPath, outputPath);
            if (result) {
                totalOriginalSize += result.originalSize;
                totalCompressedSize += result.compressedSize;
                successCount++;
            }
        } else {
            console.log(`⚠️  Image not found: ${inputPath}`);
        }
    }
    
    // Compress contract template images
    const contractDirs = ['smlouva1', 'smlouva2'];
    
    for (const contractDir of contractDirs) {
        const contractPath = path.join(templatesDir, contractDir);
        const compressedContractPath = path.join(compressedDir, contractDir);
        
        if (fs.existsSync(contractPath)) {
            // Create compressed contract directory
            if (!fs.existsSync(compressedContractPath)) {
                fs.mkdirSync(compressedContractPath, { recursive: true });
            }
            
            // Get all JPG files in contract directory
            const contractImages = fs.readdirSync(contractPath)
                .filter(file => file.toLowerCase().endsWith('.jpg'));
            
            console.log(`📄 Compressing ${contractDir} images...`);
            
            for (const imageFile of contractImages) {
                const inputPath = path.join(contractPath, imageFile);
                const outputPath = path.join(compressedContractPath, imageFile);
                
                const result = await compressImage(inputPath, outputPath);
                if (result) {
                    totalOriginalSize += result.originalSize;
                    totalCompressedSize += result.compressedSize;
                    successCount++;
                }
            }
        }
    }
    
    // Summary
    console.log('📊 COMPRESSION SUMMARY:');
    console.log('========================');
    console.log(`Images processed: ${successCount}`);
    console.log(`Total original size: ${(totalOriginalSize / 1024 / 1024).toFixed(2)}MB`);
    console.log(`Total compressed size: ${(totalCompressedSize / 1024 / 1024).toFixed(2)}MB`);
    console.log(`Total reduction: ${((1 - totalCompressedSize / totalOriginalSize) * 100).toFixed(1)}%`);
    console.log(`\n✅ Compressed images saved to: ${compressedDir}`);
}

// Run the compression if this script is executed directly
if (require.main === module) {
    compressAllTemplateImages()
        .then(() => {
            console.log('\n🎉 Image compression completed!');
            process.exit(0);
        })
        .catch((error) => {
            console.error('\n❌ Image compression failed:', error);
            process.exit(1);
        });
}

module.exports = { compressImage, compressAllTemplateImages };
