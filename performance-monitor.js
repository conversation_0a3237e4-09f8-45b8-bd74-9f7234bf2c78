// Performance monitoring middleware
const performanceMonitor = {
    // Track request timing (safe version)
    requestTimer: (req, res, next) => {
        const start = Date.now();

        // Listen for response finish to calculate timing
        res.once('finish', () => {
            try {
                const duration = Date.now() - start;

                // Only log slow requests to avoid spam
                if (duration > 2000) {
                    console.warn(`SLOW REQUEST: ${req.method} ${req.url} took ${duration}ms`);
                }
            } catch (error) {
                // Ignore timing errors
            }
        });

        next();
    },

    // Memory usage monitoring
    memoryMonitor: () => {
        const used = process.memoryUsage();
        const memoryInfo = {
            rss: Math.round(used.rss / 1024 / 1024 * 100) / 100,
            heapTotal: Math.round(used.heapTotal / 1024 / 1024 * 100) / 100,
            heapUsed: Math.round(used.heapUsed / 1024 / 1024 * 100) / 100,
            external: Math.round(used.external / 1024 / 1024 * 100) / 100
        };
        
        // Warn if memory usage is high
        if (memoryInfo.heapUsed > 500) { // 500MB threshold
            console.warn(`HIGH MEMORY USAGE: ${memoryInfo.heapUsed}MB heap used`);
        }
        
        return memoryInfo;
    },

    // Start periodic memory monitoring
    startMemoryMonitoring: (intervalMs = 60000) => { // Default: 1 minute
        setInterval(() => {
            const memory = performanceMonitor.memoryMonitor();
            if (process.env.NODE_ENV !== 'production') {
                console.log(`Memory usage: ${memory.heapUsed}MB / ${memory.heapTotal}MB`);
            }
        }, intervalMs);
    }
};

module.exports = performanceMonitor;
