// Simple email test without PDF generation
const nodemailer = require('nodemailer');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

console.log('Testing simple email sending...');

// Configure Brevo SMTP transporter (same as in server.js)
const transporter = nodemailer.createTransport({
    host: 'smtp-relay.brevo.com',
    port: 587,
    secure: false, // TLS
    auth: {
        user: process.env.BREVO_SMTP_USER,
        pass: process.env.BREVO_SMTP_PASS
    },
    // Additional options to ensure sender name is preserved
    defaults: {
        from: '"<PERSON><PERSON><PERSON><PERSON>" <<EMAIL>>'
    }
});

async function testSimpleEmail() {
    try {
        console.log('Preparing email options...');
        
        const mailOptions = {
            from: '"Kalkulace" <<EMAIL>>',
            to: '<EMAIL>',
            subject: 'Ka<PERSON><PERSON><PERSON> zateplení - potvrzení',
            text: 'Vážený/á Test,\n\nV příloze najdete PDF s kalkulací zateplení a smlouvu o projektové dokumentaci.\n\nDěkujeme za Váš zájem,\nVáš tým',
            html: '<p>Vážený/á Test,</p><p>V příloze najdete PDF s kalkulací zateplení a smlouvu o projektové dokumentaci.</p><p>Děkujeme za Váš zájem,<br>Váš tým</p>',
            attachments: [], // No attachments for this test
            // Additional headers to ensure sender name is preserved
            headers: {
                'X-Sender': 'Kalkulace',
                'Reply-To': '"Kalkulace" <<EMAIL>>',
                'From': '"Kalkulace" <<EMAIL>>'
            }
        };

        console.log('Email options prepared:', {
            from: mailOptions.from,
            to: mailOptions.to,
            subject: mailOptions.subject,
            attachmentCount: mailOptions.attachments.length
        });

        console.log('Sending email...');
        const result = await transporter.sendMail(mailOptions);
        
        console.log('✅ Email sent successfully!');
        console.log('Message ID:', result.messageId);
        console.log('Response:', result.response);
        
        return true;
        
    } catch (error) {
        console.error('❌ Email sending failed:');
        console.error('Error type:', error.constructor.name);
        console.error('Error message:', error.message);
        console.error('Error code:', error.code);
        console.error('Error response:', error.response);
        console.error('Error responseCode:', error.responseCode);
        console.error('Error command:', error.command);
        
        return false;
    }
}

// Run the test
testSimpleEmail().then((success) => {
    if (success) {
        console.log('✅ Simple email test completed successfully');
    } else {
        console.log('❌ Simple email test failed');
    }
    process.exit(success ? 0 : 1);
}).catch((error) => {
    console.error('Unexpected error:', error);
    process.exit(1);
});
