const fs = require('fs-extra');
const path = require('path');
const PDFDocument = require('pdfkit');
const pdfPositioning = require('../config/pdf-positioning');

/**
 * Memory monitoring utility for tracking memory usage
 */
class MemoryMonitor {
    constructor() {
        this.memoryHistory = [];
        this.maxHistorySize = 100;
        this.startTime = Date.now();
    }

    /**
     * Record current memory usage
     */
    recordMemoryUsage() {
        const memoryUsage = process.memoryUsage();
        const timestamp = Date.now();

        this.memoryHistory.push({
            timestamp,
            rss: memoryUsage.rss,
            heapUsed: memoryUsage.heapUsed,
            heapTotal: memoryUsage.heapTotal,
            external: memoryUsage.external
        });

        // Keep only the last N entries
        if (this.memoryHistory.length > this.maxHistorySize) {
            this.memoryHistory.shift();
        }
    }

    /**
     * Get memory statistics
     */
    getMemoryStats() {
        this.recordMemoryUsage();

        if (this.memoryHistory.length === 0) {
            return null;
        }

        const latest = this.memoryHistory[this.memoryHistory.length - 1];
        const oldest = this.memoryHistory[0];

        return {
            current: {
                rss: Math.round(latest.rss / 1024 / 1024),
                heapUsed: Math.round(latest.heapUsed / 1024 / 1024),
                heapTotal: Math.round(latest.heapTotal / 1024 / 1024),
                external: Math.round(latest.external / 1024 / 1024)
            },
            trend: this.memoryHistory.length > 1 ? {
                rssChange: Math.round((latest.rss - oldest.rss) / 1024 / 1024),
                heapChange: Math.round((latest.heapUsed - oldest.heapUsed) / 1024 / 1024)
            } : null,
            historySize: this.memoryHistory.length,
            uptimeMinutes: Math.round((Date.now() - this.startTime) / 1000 / 60)
        };
    }

    /**
     * Get current memory snapshot
     */
    async getMemorySnapshot() {
        this.recordMemoryUsage();
        const memoryUsage = process.memoryUsage();

        return {
            timestamp: new Date().toISOString(),
            process: {
                rss: Math.round(memoryUsage.rss / 1024 / 1024),
                heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
                heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
                external: Math.round(memoryUsage.external / 1024 / 1024)
            }
        };
    }

    /**
     * Log memory summary
     */
    logMemorySummary() {
        const stats = this.getMemoryStats();
        if (stats) {
            console.log('📊 Memory Summary:');
            console.log(`   RSS: ${stats.current.rss}MB, Heap: ${stats.current.heapUsed}/${stats.current.heapTotal}MB`);
            if (stats.trend) {
                console.log(`   Trend: RSS ${stats.trend.rssChange >= 0 ? '+' : ''}${stats.trend.rssChange}MB, Heap ${stats.trend.heapChange >= 0 ? '+' : ''}${stats.trend.heapChange}MB`);
            }
            console.log(`   Uptime: ${stats.uptimeMinutes} minutes`);
        }
    }
}

// Create singleton memory monitor
const memoryMonitor = new MemoryMonitor();

class DocumentService {
    constructor() {
        this.outputDir = path.join(__dirname, '..', 'generated');
        this.templatesDir = path.join(__dirname, '..', 'public', 'templates');

        // Ensure output directory exists
        fs.ensureDirSync(this.outputDir);

        console.log('DocumentService initialized');
        console.log('Output directory:', this.outputDir);
        console.log('Templates directory:', this.templatesDir);
    }

    /**
     * Prepare template data for PDF generation
     * @param {Object} formData - Raw form data
     * @returns {Object} - Processed template data
     */
    prepareTemplateData(formData) {
        // Format date of birth
        let formattedDateOfBirth = '';
        if (formData.dateOfBirth) {
            const date = new Date(formData.dateOfBirth);
            if (!isNaN(date.getTime())) {
                formattedDateOfBirth = date.toLocaleDateString('cs-CZ');
            }
        }

        return {
            customerName: formData.customerName || '',
            customerAddress: formData.customerAddress || '',
            dateOfBirth: formattedDateOfBirth,
            realizationAddress: formData.realizationAddress || '',
            email: formData.email || '',
            phone: formData.phone || ''
        };
    }

    /**
     * Get logo as base64 string for embedding in documents
     * @returns {string|null} - Base64 encoded logo or null if not found
     */
    async getLogoBase64() {
        try {
            const logoPath = path.join(this.templatesDir, 'logo.png');
            if (await fs.pathExists(logoPath)) {
                const logoBuffer = await fs.readFile(logoPath);
                return `data:image/png;base64,${logoBuffer.toString('base64')}`;
            }
            return null;
        } catch (error) {
            console.warn('Could not load logo:', error.message);
            return null;
        }
    }

    /**
     * Generate contract PDF using template images
     * @param {string} outputName - Name for the output file
     * @param {Object} formData - Form data to populate
     * @param {string} templateFolder - Template folder name (default: 'smlouva1')
     * @returns {Object} - Paths to generated files
     */
    async generateContractPDF(outputName, formData, templateFolder = 'smlouva1') {
        try {
            console.log('Generating contract PDF with template images...');
            console.log('Template folder:', templateFolder);

            // Prepare template data
            const templateData = this.prepareTemplateData(formData);
            const pdfPath = path.join(this.outputDir, `${outputName}.pdf`);

            // Create PDF document with compression
            const doc = new PDFDocument({
                size: 'A4',
                margin: 0,
                compress: true,
                pdfVersion: '1.4'
            });

            // Register font for Czech characters support
            try {
                doc.registerFont('dejavu', path.join(__dirname, '..', 'fonts', 'DejaVuSans.ttf'));
                console.log('DejaVu font registered successfully for contract PDF');
            } catch (err) {
                console.error('Error registering DejaVu font for contract PDF:', err);
                // Fallback to NotoSans if DejaVu fails
                try {
                    doc.registerFont('noto', path.join(__dirname, '..', 'fonts', 'NotoSans-Regular.ttf'));
                    console.log('NotoSans font registered as fallback for contract PDF');
                } catch (fallbackErr) {
                    console.error('Error registering fallback font for contract PDF:', fallbackErr);
                }
            }

            // Pipe to file
            const stream = fs.createWriteStream(pdfPath);
            doc.pipe(stream);

            // Template images directory - use compressed images for smaller file sizes
            const templatesDir = path.join(__dirname, '..', 'public', 'templates', 'compressed', templateFolder);

            // Select positioning config based on template folder
            const positioning = pdfPositioning[templateFolder] || pdfPositioning.smlouva1;
            console.log(`Using positioning config for template: ${templateFolder}`);

            // Add all 6 pages with template images
            for (let i = 0; i < 6; i++) {
                const imagePath = path.join(templatesDir, `${i}.jpg`);

                if (i > 0) {
                    doc.addPage();
                }

                // Add compressed template image as background
                if (fs.existsSync(imagePath)) {
                    doc.image(imagePath, 0, 0, {
                        width: 595.28, // A4 width in points
                        height: 841.89 // A4 height in points
                    });
                } else {
                    console.warn(`Template image not found: ${imagePath}`);
                }

                // Add text overlays for first page (page 0)
                if (i === 0) {
                    // Set font for Czech character support
                    try {
                        doc.font('dejavu');
                    } catch (err) {
                        try {
                            doc.font('noto');
                        } catch (fallbackErr) {
                            console.warn('Using default font - Czech characters may not display correctly');
                        }
                    }

                    doc.fontSize(positioning.firstPage.customerName.fontSize)
                       .fillColor(positioning.firstPage.customerName.color);

                    // Customer name - positioned using config coordinates
                    if (templateData.customerName) {
                        const pos = positioning.firstPage.customerName;
                        doc.text(templateData.customerName, pos.x, pos.y, { width: pos.maxWidth });
                    }

                    // Customer address - positioned using config coordinates
                    if (templateData.customerAddress) {
                        const pos = positioning.firstPage.customerAddress;
                        doc.text(templateData.customerAddress, pos.x, pos.y, { width: pos.maxWidth });
                    }

                    // Date of birth - positioned using config coordinates
                    if (templateData.dateOfBirth) {
                        const pos = positioning.firstPage.dateOfBirth;
                        doc.text(templateData.dateOfBirth, pos.x, pos.y, { width: pos.maxWidth });
                    }

                    // Email and Phone - positioned on the same line
                    if (templateData.email || templateData.phone) {
                        const emailPos = positioning.firstPage.email;

                        // Create combined text: email, phone
                        let combinedText = '';
                        if (templateData.email) {
                            combinedText += templateData.email;
                        }
                        if (templateData.phone) {
                            if (combinedText) combinedText += ', ';
                            combinedText += templateData.phone;
                        }

                        // Add the combined text on one line
                        if (combinedText) {
                            doc.text(combinedText, emailPos.x, emailPos.y, { width: 350 });
                        }
                    }

                    // Realization address - positioned using config coordinates
                    if (templateData.realizationAddress) {
                        const pos = positioning.firstPage.realizationAddress;
                        doc.text(templateData.realizationAddress, pos.x, pos.y, { width: pos.maxWidth });
                    }
                }

                // Add signature on last page (page 5)
                if (i === 5 && templateData.customerName) {
                    // Set font for Czech character support in signature
                    try {
                        doc.font('dejavu');
                    } catch (err) {
                        try {
                            doc.font('noto');
                        } catch (fallbackErr) {
                            console.warn('Using default font for signature - Czech characters may not display correctly');
                        }
                    }

                    const pos = positioning.lastPage.customerSignature;
                    doc.fontSize(pos.fontSize)
                       .fillColor(pos.color)
                       .text(templateData.customerName, pos.x, pos.y, {
                           width: pos.maxWidth,
                           align: pos.align
                       });
                }
            }

            // Finalize the PDF
            doc.end();

            // Wait for the stream to finish
            await new Promise((resolve, reject) => {
                stream.on('finish', resolve);
                stream.on('error', reject);
            });

            console.log('Contract PDF generated successfully:', pdfPath);

            return {
                pdfPath: pdfPath,
                success: true,
                message: 'Contract PDF generated successfully'
            };

        } catch (error) {
            console.error('Contract PDF generation error:', error);
            throw error;
        }
    }

    /**
     * Generate image-based contract PDF (alias for generateContractPDF)
     * @param {string} outputName - Name for the output file
     * @param {Object} formData - Form data to populate
     * @param {string} userRole - User role to determine template (admin/editor uses smlouva1, others use smlouva2)
     * @returns {Object} - Paths to generated files
     */
    async generateImageBasedContract(outputName, formData, userRole = 'user') {
        try {
            console.log('Generating contract PDF using image templates...');
            console.log('User role for template selection:', userRole);

            // Determine template folder based on user role
            let templateFolder = 'smlouva2'; // Default for regular users
            if (userRole === 'admin' || userRole === 'editor' || userRole === 'smlouva1') {
                templateFolder = 'smlouva1';
            }

            console.log('Selected template folder:', templateFolder);

            // Generate the contract PDF
            const result = await this.generateContractPDF(outputName, formData, templateFolder);

            // Add additional metadata for compatibility
            if (result.success) {
                console.log('Image-based contract PDF generated successfully');
                return {
                    ...result,
                    templateFolder: templateFolder,
                    userRole: userRole
                };
            } else {
                return result;
            }

        } catch (error) {
            console.error('Image-based contract generation error:', error);
            return {
                success: false,
                error: error.message,
                errorType: 'generation_error'
            };
        }
    }

    /**
     * Clean up old files
     * @param {number} days - Number of days to keep files
     */
    async cleanupOldFiles(days = 7) {
        try {
            console.log(`Starting file cleanup (removing files older than ${days} days)...`);

            const files = await fs.readdir(this.outputDir);
            const cutoffTime = Date.now() - (days * 24 * 60 * 60 * 1000);
            let cleanedCount = 0;
            let totalSize = 0;

            for (const file of files) {
                try {
                    const filePath = path.join(this.outputDir, file);
                    const stats = await fs.stat(filePath);

                    if (stats.mtime.getTime() < cutoffTime) {
                        totalSize += stats.size;
                        await fs.remove(filePath);
                        cleanedCount++;
                        console.log(`Cleaned up old file: ${file} (${Math.round(stats.size / 1024)}KB)`);
                    }
                } catch (fileError) {
                    console.error(`Error processing file ${file}:`, fileError);
                }
            }

            if (cleanedCount > 0) {
                console.log(`File cleanup completed: ${cleanedCount} files removed, ${Math.round(totalSize / 1024 / 1024 * 100) / 100}MB freed`);
            } else {
                console.log('File cleanup completed: No old files to remove');
            }

            // Force garbage collection after file cleanup
            if (global.gc) {
                global.gc();
            }

        } catch (error) {
            console.error('Error cleaning up old files:', error);
        }
    }

    /**
     * Get memory statistics
     */
    async getMemoryStatistics() {
        return memoryMonitor.getMemoryStats();
    }

    /**
     * Get current memory snapshot
     */
    async getMemorySnapshot() {
        return await memoryMonitor.getMemorySnapshot();
    }

    /**
     * Force memory monitoring summary log
     */
    logMemorySummary() {
        memoryMonitor.logMemorySummary();
    }

    /**
     * Cleanup method for graceful shutdown
     */
    async cleanup() {
        try {
            console.log('DocumentService cleanup started...');

            // Clean up old files
            await this.cleanupOldFiles(1); // Clean files older than 1 day on shutdown

            // Force garbage collection if available
            if (global.gc) {
                global.gc();
            }

            console.log('DocumentService cleanup completed');
        } catch (error) {
            console.error('Error during DocumentService cleanup:', error);
        }
    }
}

module.exports = new DocumentService();
