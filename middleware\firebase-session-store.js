const { Store } = require('express-session');
const { db } = require('../firebase-admin');

/**
 * Firebase Session Store for express-session
 * Stores sessions in Firestore to prevent memory leaks and enable scaling
 */
class FirebaseSessionStore extends Store {
    constructor(options = {}) {
        super(options);
        this.collection = options.collection || 'sessions';
        this.ttl = options.ttl || 86400; // 24 hours in seconds

        console.log(`Firebase Session Store initialized (collection: ${this.collection}, TTL: ${this.ttl}s)`);

        // Start cleanup routine
        this.startCleanup();
    }

    /**
     * Get session from Firestore
     */
    async get(sessionId, callback) {
        try {
            const doc = await db.collection(this.collection).doc(sessionId).get();

            if (!doc.exists) {
                return callback(null, null);
            }

            const sessionData = doc.data();

            // Check if session has expired
            if (sessionData.expires && new Date() > sessionData.expires.toDate()) {
                await this.destroy(sessionId, () => {});
                return callback(null, null);
            }

            callback(null, sessionData.session);
        } catch (error) {
            console.error('Error getting session from Firebase:', error);
            callback(error);
        }
    }

    /**
     * Set session in Firestore
     */
    async set(sessionId, session, callback) {
        try {
            const expires = new Date(Date.now() + (this.ttl * 1000));

            // Serialize session data to plain object to avoid Firestore prototype issues
            const serializedSession = JSON.parse(JSON.stringify(session));

            await db.collection(this.collection).doc(sessionId).set({
                session: serializedSession,
                expires: expires,
                lastAccess: new Date()
            });

            callback && callback(null);
        } catch (error) {
            console.error('Error setting session in Firebase:', error);
            callback && callback(error);
        }
    }

    /**
     * Destroy session in Firestore
     */
    async destroy(sessionId, callback) {
        try {
            await db.collection(this.collection).doc(sessionId).delete();
            callback && callback(null);
        } catch (error) {
            console.error('Error destroying session in Firebase:', error);
            callback && callback(error);
        }
    }

    /**
     * Touch session (update last access time)
     */
    async touch(sessionId, session, callback) {
        try {
            const expires = new Date(Date.now() + (this.ttl * 1000));

            // Serialize session data to plain object to avoid Firestore prototype issues
            const serializedSession = JSON.parse(JSON.stringify(session));

            await db.collection(this.collection).doc(sessionId).update({
                session: serializedSession,
                expires: expires,
                lastAccess: new Date()
            });

            callback && callback(null);
        } catch (error) {
            console.error('Error touching session in Firebase:', error);
            callback && callback(error);
        }
    }

    /**
     * Get all session IDs
     */
    async all(callback) {
        try {
            const snapshot = await db.collection(this.collection).get();
            const sessions = {};
            
            snapshot.forEach(doc => {
                const data = doc.data();
                if (!data.expires || new Date() <= data.expires.toDate()) {
                    sessions[doc.id] = data.session;
                }
            });

            callback && callback(null, sessions);
        } catch (error) {
            console.error('Error getting all sessions from Firebase:', error);
            callback && callback(error);
        }
    }

    /**
     * Get session count
     */
    async length(callback) {
        try {
            const snapshot = await db.collection(this.collection).get();
            let count = 0;
            
            snapshot.forEach(doc => {
                const data = doc.data();
                if (!data.expires || new Date() <= data.expires.toDate()) {
                    count++;
                }
            });

            callback && callback(null, count);
        } catch (error) {
            console.error('Error getting session count from Firebase:', error);
            callback && callback(error);
        }
    }

    /**
     * Clear all sessions
     */
    async clear(callback) {
        try {
            const snapshot = await db.collection(this.collection).get();
            const batch = db.batch();
            
            snapshot.forEach(doc => {
                batch.delete(doc.ref);
            });

            await batch.commit();
            callback && callback(null);
        } catch (error) {
            console.error('Error clearing sessions from Firebase:', error);
            callback && callback(error);
        }
    }

    /**
     * Start automatic cleanup of expired sessions
     */
    startCleanup() {
        // Clean up expired sessions every hour
        setInterval(async () => {
            try {
                await this.cleanupExpiredSessions();
            } catch (error) {
                console.error('Error during session cleanup:', error);
            }
        }, 60 * 60 * 1000); // 1 hour

        console.log('Firebase session cleanup started (every hour)');
    }

    /**
     * Clean up expired sessions
     */
    async cleanupExpiredSessions() {
        try {
            const now = new Date();
            const snapshot = await db.collection(this.collection)
                .where('expires', '<', now)
                .get();

            if (snapshot.empty) {
                console.log('Session cleanup: No expired sessions to remove');
                return;
            }

            const batch = db.batch();
            let count = 0;

            snapshot.forEach(doc => {
                batch.delete(doc.ref);
                count++;
            });

            await batch.commit();
            console.log(`Session cleanup: Removed ${count} expired sessions`);
        } catch (error) {
            console.error('Error cleaning up expired sessions:', error);
        }
    }
}

module.exports = FirebaseSessionStore;
