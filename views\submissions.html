<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spr<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> App</title>
    <link rel="stylesheet" href="/style.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- Add jQuery for simpler AJAX handling -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        /* Make container wider for more submissions */
        .container {
            max-width: 1400px !important;
            width: 95% !important;
        }

        /* Submissions page styles */
        .submissions-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background-color: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--box-shadow);
            font-size: 14px;
        }

        .submissions-table th,
        .submissions-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }

        .submissions-table th {
            background-color: var(--primary-color);
            color: white;
            font-weight: 500;
        }

        .submissions-table tr:last-child td {
            border-bottom: none;
        }

        .submissions-table tr:hover {
            background-color: #f8fafc;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-approved {
            background-color: #10b981;
            color: white;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
            margin-bottom: 8px;
        }

        /* Removed unused PDF button styles */

        .action-btn {
            padding: 6px 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-family: 'Poppins', sans-serif;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 2px;
            display: inline-flex;
            align-items: center;
            gap: 4px;
            text-decoration: none;
        }



        .send-email-btn {
            background-color: #10b981;
            color: white;
        }

        .pdf-btn {
            background-color: #3b82f6;
            color: white;
        }

        .contract-pdf-btn {
            background-color: #1e3a8a;
            color: white;
        }

        .delete-btn {
            background-color: #6b7280;
            color: white;
        }

        .send-email-btn {
            background-color: #10b981;
            color: white;
        }

        .action-btn:hover {
            opacity: 0.9;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .action-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .user-info-cell {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .user-avatar-small {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            object-fit: cover;
        }

        .empty-state {
            text-align: center;
            padding: 40px;
            color: var(--text-light);
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 15px;
            color: #d1d5db;
        }

        /* Filters and search styles */
        .filters-container {
            background: white;
            padding: 20px;
            border-radius: var(--border-radius);
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .filters-row {
            display: flex;
            gap: 15px;
            align-items: end;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            min-width: 150px;
        }

        .filter-group label {
            font-weight: 500;
            margin-bottom: 5px;
            color: var(--text-dark);
            font-size: 14px;
        }

        .filter-group input,
        .filter-group select {
            padding: 8px 12px;
            border: 2px solid #e5e7eb;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .filter-group input:focus,
        .filter-group select:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .search-group {
            flex: 1;
            min-width: 250px;
        }

        .search-input-container {
            position: relative;
        }

        .search-input-container i {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #6b7280;
        }

        .search-input-container input {
            padding-left: 40px;
            width: 100%;
        }

        .filter-actions {
            display: flex;
            gap: 10px;
        }

        .btn-filter {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #1e40af;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        /* Pagination styles */
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
            padding: 20px;
            background: white;
            border-radius: var(--border-radius);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .pagination-info {
            color: var(--text-light);
            font-size: 14px;
        }

        .pagination-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .pagination-btn {
            padding: 8px 12px;
            border: 2px solid #e5e7eb;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            text-decoration: none;
            color: var(--text-dark);
        }

        .pagination-btn:hover:not(:disabled) {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .page-size-selector {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .page-size-selector select {
            padding: 6px 10px;
            border: 2px solid #e5e7eb;
            border-radius: 6px;
            font-size: 14px;
        }

        /* Loading overlay */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }

        .submissions-container {
            position: relative;
        }

        .pagination-ellipsis {
            padding: 8px 4px;
            color: var(--text-light);
            font-size: 14px;
        }

        /* Confirmation modal styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal {
            background: white;
            border-radius: 8px;
            padding: 24px;
            max-width: 400px;
            width: 90%;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .modal h3 {
            margin: 0 0 16px 0;
            color: #ef4444;
        }

        .modal p {
            margin: 0 0 20px 0;
            color: #6b7280;
        }

        .modal-buttons {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
        }

        .modal-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .modal-btn-danger {
            background-color: #ef4444;
            color: white;
        }

        .modal-btn-secondary {
            background-color: #6b7280;
            color: white;
        }

        .modal-btn:hover {
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="dashboard-header">
            <h1>Správa nabídek</h1>
            <a href="/dashboard" class="back-link">
                <i class="fas fa-arrow-left"></i> Zpět
            </a>
        </div>

        <div id="alertContainer"></div>

        <!-- Filters and Search -->
        <div class="filters-container">
            <div class="filters-row">
                <div class="search-group">
                    <label for="searchInput">Hledat</label>
                    <div class="search-input-container">
                        <i class="fas fa-search"></i>
                        <input type="text" id="searchInput" placeholder="Hledat podle jména, emailu, telefonu nebo adresy...">
                    </div>
                </div>



                <div class="filter-group">
                    <label for="dateFromFilter">Od data</label>
                    <input type="date" id="dateFromFilter">
                </div>

                <div class="filter-group">
                    <label for="dateToFilter">Do data</label>
                    <input type="date" id="dateToFilter">
                </div>

                <div class="filter-actions">
                    <button type="button" class="btn-filter btn-primary" id="applyFilters">
                        <i class="fas fa-filter"></i> Filtrovat
                    </button>
                    <button type="button" class="btn-filter btn-secondary" id="clearFilters">
                        <i class="fas fa-times"></i> Vymazat
                    </button>
                </div>
            </div>
        </div>

        <!-- Submissions Container -->
        <div class="submissions-container">
            <div id="submissionsContainer">
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin fa-2x"></i>
                </div>
            </div>
        </div>

        <!-- Pagination Container -->
        <div id="paginationContainer" style="display: none;">
            <div class="pagination-container">
                <div class="pagination-info">
                    <span id="paginationInfo">Zobrazeno 0-0 z 0 nabídek</span>
                </div>

                <div class="pagination-controls">
                    <button class="pagination-btn" id="prevPage" disabled>
                        <i class="fas fa-chevron-left"></i> Předchozí
                    </button>

                    <div id="pageNumbers"></div>

                    <button class="pagination-btn" id="nextPage" disabled>
                        Další <i class="fas fa-chevron-right"></i>
                    </button>
                </div>

                <div class="page-size-selector">
                    <label for="pageSizeSelect">Zobrazit:</label>
                    <select id="pageSizeSelect">
                        <option value="10">10</option>
                        <option value="25" selected>25</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                    <span>na stránku</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Helper function to get reliable profile image URL
        function getProfileImageUrl(profileImage, displayName) {
            if (!profileImage || profileImage.trim() === '') {
                return getInitialsAvatar(displayName || 'User');
            }

            // Fix Google profile image URLs to use a more reliable format
            if (profileImage.includes('googleusercontent.com')) {
                // Remove size parameter and add a reliable size
                const baseUrl = profileImage.split('=')[0];
                return baseUrl + '=s96-c';
            }

            return profileImage;
        }

        // Helper function to generate initials avatar
        function getInitialsAvatar(name) {
            const initials = (name || 'User').split(' ').map(n => n[0]).join('').substring(0, 2).toUpperCase();
            return `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&background=007bff&color=fff&size=96&bold=true`;
        }

        // Global variables for pagination and filtering
        let currentPage = 1;
        let currentLimit = 25;
        let currentFilters = {
            search: '',
            dateFrom: '',
            dateTo: ''
        };
        let searchTimeout;

        $(document).ready(function() {
            console.log('Document ready - initializing submissions page');

            try {
                // Initialize the page
                initializeFilters();
                loadSubmissions();
            } catch (error) {
                console.error('Error in document ready:', error);
                $('#submissionsContainer').html(`
                    <div class="empty-state">
                        <i class="fas fa-exclamation-triangle"></i>
                        <p>Chyba při načítání stránky.</p>
                        <small>Zkuste obnovit stránku.</small>
                    </div>
                `);
            }
        });

        // Initialize filter event handlers
        function initializeFilters() {
            // Search input with debouncing
            $('#searchInput').on('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function() {
                    currentFilters.search = $('#searchInput').val().trim();
                    currentPage = 1; // Reset to first page
                    loadSubmissions();
                }, 500); // 500ms delay
            });



            // Date filters
            $('#dateFromFilter, #dateToFilter').on('change', function() {
                currentFilters.dateFrom = $('#dateFromFilter').val();
                currentFilters.dateTo = $('#dateToFilter').val();
                currentPage = 1;
                loadSubmissions();
            });

            // Apply filters button
            $('#applyFilters').on('click', function() {
                currentFilters = {
                    search: $('#searchInput').val().trim(),
                    dateFrom: $('#dateFromFilter').val(),
                    dateTo: $('#dateToFilter').val()
                };
                currentPage = 1;
                loadSubmissions();
            });

            // Clear filters button
            $('#clearFilters').on('click', function() {
                $('#searchInput').val('');
                $('#dateFromFilter').val('');
                $('#dateToFilter').val('');
                currentFilters = {
                    search: '',
                    dateFrom: '',
                    dateTo: ''
                };
                currentPage = 1;
                loadSubmissions();
            });

            // Page size selector
            $('#pageSizeSelect').on('change', function() {
                currentLimit = parseInt($(this).val());
                currentPage = 1;
                loadSubmissions();
            });

            // Pagination buttons
            $('#prevPage').on('click', function() {
                if (currentPage > 1) {
                    currentPage--;
                    loadSubmissions();
                }
            });

            $('#nextPage').on('click', function() {
                currentPage++;
                loadSubmissions();
            });
        }

        // Load submissions with current filters and pagination
        function loadSubmissions() {
            try {
                console.log('loadSubmissions called with:', {
                    currentPage,
                    currentLimit,
                    currentFilters
                });

                // Show loading state
                showLoadingState();

            // Build query parameters
            const params = new URLSearchParams({
                page: currentPage,
                limit: currentLimit,
                search: currentFilters.search,
                dateFrom: currentFilters.dateFrom,
                dateTo: currentFilters.dateTo
            });

            const url = '/submissions/list?' + params.toString();
            console.log('Making AJAX request to:', url);

            // Fetch submissions using jQuery AJAX
            $.ajax({
                url: url,
                type: 'GET',
                dataType: 'json',
                xhrFields: {
                    withCredentials: true
                },
                success: function(response) {
                    console.log('Submissions loaded successfully:', response);
                    displaySubmissions(response.submissions);
                    updatePagination(response.pagination);
                    hideLoadingState();
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText,
                        error: error
                    });
                    showAlert('Failed to load submissions. Please try again.', 'danger');
                    hideLoadingState();
                }
            });
            } catch (error) {
                console.error('Error in loadSubmissions:', error);
                $('#submissionsContainer').html(`
                    <div class="empty-state">
                        <i class="fas fa-exclamation-triangle"></i>
                        <p>Chyba při načítání nabídek.</p>
                        <small>Zkuste obnovit stránku.</small>
                    </div>
                `);
                hideLoadingState();
            }
        }

        // Show loading state
        function showLoadingState() {
            if ($('.loading-overlay').length === 0) {
                $('.submissions-container').append(`
                    <div class="loading-overlay">
                        <div class="loading-spinner">
                            <i class="fas fa-spinner fa-spin fa-2x"></i>
                        </div>
                    </div>
                `);
            }
        }

        // Hide loading state
        function hideLoadingState() {
            $('.loading-overlay').remove();
        }

        // Display submissions
        function displaySubmissions(submissions) {
            if (!submissions || submissions.length === 0) {
                $('#submissionsContainer').html(`
                    <div class="empty-state">
                        <i class="fas fa-inbox"></i>
                        <p>Žádné nabídky nenalezeny.</p>
                        <small>Zkuste změnit filtry nebo vyhledávací kritéria.</small>
                    </div>
                `);
                $('#paginationContainer').hide();
                return;
            }

            let html = `
                <table class="submissions-table">
                    <thead>
                        <tr>
                            <th>Uživatel</th>
                            <th>Telefon</th>
                            <th>Adresa</th>
                            <th>Datum</th>
                            <th>Akce</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            $.each(submissions, function(i, submission) {
                // Handle date safely
                let date = 'Unknown date';
                try {
                    if (submission.createdAt) {
                        date = new Date(submission.createdAt).toLocaleString('cs-CZ');
                    }
                } catch (error) {
                    console.error('Error formatting date:', error);
                }

                html += `
                    <tr data-id="${submission.id}">
                        <td>
                            <div class="user-info-cell">
                                <img src="${getProfileImageUrl(submission.userProfileImage, submission.name)}" alt="Uživatel" class="user-avatar-small" onerror="this.src=getInitialsAvatar('${submission.name || 'User'}')">
                                <div>
                                    <div>${submission.name}</div>
                                    <div>${submission.email}</div>
                                </div>
                            </div>
                        </td>
                        <td>${submission.phone || 'N/A'}</td>
                        <td>${submission.address || 'N/A'}</td>
                        <td>${date}</td>
                        <td>
                `;

                // Add action buttons (simplified since all submissions are automatically approved)
                html += `
                    <div class="action-buttons">
                        <button class="action-btn send-email-btn" data-id="${submission.id}" title="Odeslat email">
                            <i class="fas fa-envelope"></i> Odeslat Email
                        </button>
                        <button class="action-btn pdf-btn" data-id="${submission.id}" title="Kalkulace PDF">
                            <i class="fas fa-file-pdf"></i> Kalkulace PDF
                        </button>
                        <button class="action-btn contract-pdf-btn" data-id="${submission.id}" title="Smlouva PDF">
                            <i class="fas fa-file-contract"></i> Smlouva PDF
                        </button>
                        <button class="action-btn delete-btn" data-id="${submission.id}" title="Smazat">
                            <i class="fas fa-trash"></i> Smazat
                        </button>
                    </div>
                `;

                html += `
                        </td>
                    </tr>
                `;
            });

            html += `
                    </tbody>
                </table>
            `;

            $('#submissionsContainer').html(html);
            $('#paginationContainer').show();

            // Add event listeners for send email button
            $(document).off('click', '.send-email-btn').on('click', '.send-email-btn', function() {
                const submissionId = $(this).data('id');
                sendEmail(submissionId);
            });

            // Add event listener for PDF button
            $(document).off('click', '.pdf-btn').on('click', '.pdf-btn', function() {
                const submissionId = $(this).data('id');
                viewCalculationPDF(submissionId);
            });

            // Add event listener for contract PDF button
            $(document).off('click', '.contract-pdf-btn').on('click', '.contract-pdf-btn', function() {
                const submissionId = $(this).data('id');
                viewContractPDF(submissionId);
            });

            // Add event listener for delete button
            $(document).off('click', '.delete-btn').on('click', '.delete-btn', function() {
                const submissionId = $(this).data('id');
                showDeleteConfirmation(submissionId);
            });
        }

        // Update pagination controls
        function updatePagination(pagination) {
            if (!pagination) return;

            // Update pagination info
            const start = ((pagination.currentPage - 1) * pagination.limit) + 1;
            const end = Math.min(pagination.currentPage * pagination.limit, pagination.totalCount);
            $('#paginationInfo').text(`Zobrazeno ${start}-${end} z ${pagination.totalCount} nabídek`);

            // Update previous button
            $('#prevPage').prop('disabled', !pagination.hasPrev);

            // Update next button
            $('#nextPage').prop('disabled', !pagination.hasNext);

            // Generate page numbers
            generatePageNumbers(pagination);
        }

        // Generate page number buttons
        function generatePageNumbers(pagination) {
            const pageNumbers = $('#pageNumbers');
            pageNumbers.empty();

            const totalPages = pagination.totalPages;
            const currentPage = pagination.currentPage;

            // Show max 5 page numbers
            let startPage = Math.max(1, currentPage - 2);
            let endPage = Math.min(totalPages, startPage + 4);

            // Adjust start if we're near the end
            if (endPage - startPage < 4) {
                startPage = Math.max(1, endPage - 4);
            }

            // Add first page and ellipsis if needed
            if (startPage > 1) {
                pageNumbers.append(`
                    <button class="pagination-btn page-number" data-page="1">1</button>
                `);
                if (startPage > 2) {
                    pageNumbers.append('<span class="pagination-ellipsis">...</span>');
                }
            }

            // Add page numbers
            for (let i = startPage; i <= endPage; i++) {
                const activeClass = i === currentPage ? 'active' : '';
                pageNumbers.append(`
                    <button class="pagination-btn page-number ${activeClass}" data-page="${i}">${i}</button>
                `);
            }

            // Add last page and ellipsis if needed
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    pageNumbers.append('<span class="pagination-ellipsis">...</span>');
                }
                pageNumbers.append(`
                    <button class="pagination-btn page-number" data-page="${totalPages}">${totalPages}</button>
                `);
            }

            // Add click handlers for page numbers (using event delegation)
            $(document).off('click', '.page-number').on('click', '.page-number', function() {
                const page = parseInt($(this).data('page'));
                if (page !== pagination.currentPage) {
                    currentPage = page;
                    loadSubmissions();
                }
            });
        }



        // Send email for approved submission
        function sendEmail(submissionId) {
            // Show loading state
            const button = $(`.send-email-btn[data-id="${submissionId}"]`);
            const originalText = button.html();
            button.html('<i class="fas fa-spinner fa-spin"></i> Sending...');
            button.prop('disabled', true);

            $.ajax({
                url: '/send-approved-email',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ submissionId }),
                success: function(data) {
                    showAlert('Email sent successfully!', 'success');
                    // Reload current page instead of full page reload
                    setTimeout(function() {
                        loadSubmissions();
                    }, 1000);
                },
                error: function(xhr, status, error) {
                    showAlert('Failed to send email: ' + error, 'danger');
                    // Restore button
                    button.html(originalText);
                    button.prop('disabled', false);
                }
            });
        }

        // View calculation PDF
        function viewCalculationPDF(submissionId) {
            window.open(`/debug-template-pdf/${submissionId}`, '_blank');
        }

        // View contract PDF - show loading then open in new tab
        function viewContractPDF(submissionId) {
            console.log('viewContractPDF called with submissionId:', submissionId);

            // Show loading state
            const button = $(`.contract-pdf-btn[data-id="${submissionId}"]`);
            const originalText = button.html();
            button.html('<i class="fas fa-spinner fa-spin"></i> Generating...');
            button.prop('disabled', true);

            // Open the PDF URL directly in new tab (the server will handle generation)
            const pdfUrl = `/view-contract-pdf/${submissionId}`;
            console.log('Opening PDF URL:', pdfUrl);

            // Open in new tab
            const newWindow = window.open(pdfUrl, '_blank');

            // Check if popup was blocked
            if (!newWindow || newWindow.closed || typeof newWindow.closed == 'undefined') {
                console.warn('Popup blocked, trying alternative method...');
                showAlert('Popup blocked. Opening PDF in current tab...', 'warning');
                setTimeout(() => {
                    window.location.href = pdfUrl;
                }, 1000);
            } else {
                console.log('PDF opened successfully in new tab');
                showAlert('Contract PDF is being generated...', 'info');
            }

            // Restore button after a short delay
            setTimeout(() => {
                button.html(originalText);
                button.prop('disabled', false);
            }, 2000);
        }

        // Show delete confirmation modal
        function showDeleteConfirmation(submissionId) {
            const modal = $(`
                <div class="modal-overlay" id="deleteModal">
                    <div class="modal">
                        <h3><i class="fas fa-exclamation-triangle"></i> Potvrdit smazání</h3>
                        <p>Opravdu chcete smazat tuto nabídku? Tato akce je nevratná.</p>
                        <div class="modal-buttons">
                            <button class="modal-btn modal-btn-secondary" id="cancelDeleteBtn">
                                Zrušit
                            </button>
                            <button class="modal-btn modal-btn-danger" id="confirmDeleteBtn" data-submission-id="${submissionId}">
                                <i class="fas fa-trash"></i> Smazat
                            </button>
                        </div>
                    </div>
                </div>
            `);

            $('body').append(modal);

            // Add event listeners for the modal buttons
            $('#cancelDeleteBtn').on('click', function() {
                hideDeleteConfirmation();
            });

            $('#confirmDeleteBtn').on('click', function() {
                const submissionId = $(this).data('submission-id');
                deleteSubmission(submissionId);
            });
        }

        // Hide delete confirmation modal
        function hideDeleteConfirmation() {
            $('#deleteModal').remove();
        }

        // Delete submission
        function deleteSubmission(submissionId) {
            // Show loading state
            const deleteBtn = $('#deleteModal .modal-btn-danger');
            deleteBtn.html('<i class="fas fa-spinner fa-spin"></i> Mazání...');
            deleteBtn.prop('disabled', true);

            $.ajax({
                url: '/submissions/delete',
                type: 'DELETE',
                contentType: 'application/json',
                data: JSON.stringify({ submissionId }),
                xhrFields: {
                    withCredentials: true
                },
                success: function(data) {
                    hideDeleteConfirmation();
                    showAlert('Nabídka byla úspěšně smazána.', 'success');
                    // Reload current page
                    setTimeout(function() {
                        loadSubmissions();
                    }, 1000);
                },
                error: function(xhr, status, error) {
                    hideDeleteConfirmation();
                    showAlert('Chyba při mazání nabídky: ' + error, 'danger');
                }
            });
        }

        function showAlert(message, type) {
            const alertContainer = $('#alertContainer');
            const alert = $(`<div class="alert alert-${type}">${message}</div>`);

            alertContainer.empty().append(alert);

            setTimeout(function() {
                alert.fadeOut(function() {
                    $(this).remove();
                });
            }, 3000);
        }
    </script>

    <!-- Delete confirmation modal will be inserted here by JavaScript -->

</body>
</html>
