<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Úprava hodnot - Ka<PERSON>čka App</title>
    <link rel="stylesheet" href="/style.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        .pricing-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .pricing-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .pricing-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .pricing-section h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 10px;
        }

        .pricing-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px 30px;
        }

        @media (max-width: 768px) {
            .pricing-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }

        .pricing-field {
            display: flex;
            flex-direction: column;
            margin-bottom: 10px;
        }

        .pricing-field label {
            font-weight: 500;
            margin-bottom: 5px;
            color: #1f2937;
        }

        .pricing-field input {
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 5px;
            font-size: 14px;
        }

        .pricing-field input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 5px rgba(183, 58, 39, 0.3);
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 30px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #1e40af;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .btn-warning {
            background: #f59e0b;
            color: #1f2937;
        }

        .btn-warning:hover {
            background: #d97706;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 5px;
            display: none;
        }

        .alert-success {
            color: #065f46;
            background-color: #d1fae5;
            border-color: #a7f3d0;
        }

        .alert-danger {
            color: #991b1b;
            background-color: #fee2e2;
            border-color: #fecaca;
        }

        .loading {
            text-align: center;
            padding: 40px;
        }

        .loading i {
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="pricing-container">
        <div class="dashboard-header">
            <h1>Úprava hodnot</h1>
            <a href="/dashboard" class="back-link">
                <i class="fas fa-arrow-left"></i> Zpět
            </a>
        </div>

        <div id="alertContainer"></div>

        <div id="loadingIndicator" class="loading">
            <i class="fas fa-spinner fa-spin fa-2x"></i>
            <p>Loading pricing configuration...</p>
        </div>

        <div id="pricingForm" style="display: none;">
            <!-- Facade Insulation -->
            <div class="pricing-section">
                <h3>Zateplení fasády</h3>
                <div class="pricing-grid">
                    <div class="pricing-field">
                        <label for="facadePolystyrene">Polystyren (Kč/m²):</label>
                        <input type="number" id="facadePolystyrene" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="facadeMineralWool">Minerální vata (Kč/m²):</label>
                        <input type="number" id="facadeMineralWool" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="facadeRevealCost">Špalet (Kč/bm):</label>
                        <input type="number" id="facadeRevealCost" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="facadeSubsidy">Dotace (Kč/m²):</label>
                        <input type="number" id="facadeSubsidy" min="0" step="1">
                    </div>
                </div>
            </div>

            <!-- Roof Insulation -->
            <div class="pricing-section">
                <h3>Zateplení střechy</h3>
                <div class="pricing-grid">
                    <div class="pricing-field">
                        <label for="roofWithoutCladding">Bez záklopu (Kč/m²):</label>
                        <input type="number" id="roofWithoutCladding" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="roofWithCladding">Se záklopem (Kč/m²):</label>
                        <input type="number" id="roofWithCladding" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="roofSubsidy">Dotace (Kč/m²):</label>
                        <input type="number" id="roofSubsidy" min="0" step="1">
                    </div>
                </div>
            </div>

            <!-- Attic Ceiling Insulation -->
            <div class="pricing-section">
                <h3>Zateplení stropu pod nevytápěnou půdou (foukaná izolace)</h3>
                <div class="pricing-grid">
                    <div class="pricing-field">
                        <label for="atticBaseCost">Základní cena (Kč/m²):</label>
                        <input type="number" id="atticBaseCost" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="atticWithFloorRemoval">S demontáží podlahy (Kč/m²):</label>
                        <input type="number" id="atticWithFloorRemoval" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="atticOsbBoardCost">OSB deska (Kč/m²):</label>
                        <input type="number" id="atticOsbBoardCost" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="atticWalkwayCost">Revizní lávka (Kč/bm):</label>
                        <input type="number" id="atticWalkwayCost" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="atticSubsidy">Dotace (Kč/m²):</label>
                        <input type="number" id="atticSubsidy" min="0" step="1">
                    </div>
                </div>
            </div>

            <!-- Wall Insulation -->
            <div class="pricing-section">
                <h3>Zateplení stěny</h3>
                <div class="pricing-grid">
                    <div class="pricing-field">
                        <label for="wallBaseCost">Základní cena (Kč/m²):</label>
                        <input type="number" id="wallBaseCost" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="wallSubsidy">Dotace (Kč/m²):</label>
                        <input type="number" id="wallSubsidy" min="0" step="1">
                    </div>
                </div>
            </div>

            <!-- Floor Insulation -->
            <div class="pricing-section">
                <h3>Zateplení podlahy</h3>
                <div class="pricing-grid">
                    <div class="pricing-field">
                        <label for="floorBaseCost">Základní cena (Kč/m²):</label>
                        <input type="number" id="floorBaseCost" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="floorSubsidy">Dotace (Kč/m²):</label>
                        <input type="number" id="floorSubsidy" min="0" step="1">
                    </div>
                </div>
            </div>

            <!-- Windows -->
            <div class="pricing-section">
                <h3>Výplně otvorů</h3>
                <div class="pricing-grid">
                    <div class="pricing-field">
                        <label for="windowsBaseCost">Základní cena (Kč/m²):</label>
                        <input type="number" id="windowsBaseCost" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="windowsSubsidy">Dotace (Kč/m²):</label>
                        <input type="number" id="windowsSubsidy" min="0" step="1">
                    </div>
                </div>
            </div>

            <!-- Shading Technology -->
            <div class="pricing-section">
                <h3>Stínící technika</h3>
                <div class="pricing-grid">
                    <div class="pricing-field">
                        <label for="shadingAreaCost">Cena za plochu (Kč/m²):</label>
                        <input type="number" id="shadingAreaCost" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="shadingWindowWidthCost">Cena za šířku oken (Kč/bm):</label>
                        <input type="number" id="shadingWindowWidthCost" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="shadingSubsidy">Dotace (Kč/m²):</label>
                        <input type="number" id="shadingSubsidy" min="0" step="1">
                    </div>
                </div>
            </div>

            <!-- Heat Recovery -->
            <div class="pricing-section">
                <h3>Řízené větrání - rekuperace</h3>
                <div class="pricing-grid">
                    <div class="pricing-field">
                        <label for="heatRecoveryBaseCost">Základní cena (Kč):</label>
                        <input type="number" id="heatRecoveryBaseCost" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="heatRecoverySubsidy">Dotace (Kč):</label>
                        <input type="number" id="heatRecoverySubsidy" min="0" step="1">
                    </div>
                </div>
            </div>

            <!-- Rainwater -->
            <div class="pricing-section">
                <h3>Dešťová voda</h3>
                <div class="pricing-grid">
                    <div class="pricing-field">
                        <label for="rainwaterBaseCost">Základní cena (Kč):</label>
                        <input type="number" id="rainwaterBaseCost" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="rainwaterCostPerM3">Cena za m³ (Kč/m³):</label>
                        <input type="number" id="rainwaterCostPerM3" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="rainwaterBaseSubsidy">Základní dotace (Kč):</label>
                        <input type="number" id="rainwaterBaseSubsidy" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="rainwaterSubsidyPerM3">Dotace za m³ (Kč/m³):</label>
                        <input type="number" id="rainwaterSubsidyPerM3" min="0" step="1">
                    </div>
                </div>
            </div>

            <!-- Heat Pump -->
            <div class="pricing-section">
                <h3>Výměna zdroje tepla – tepelné čerpadlo</h3>
                <div class="pricing-grid">
                    <div class="pricing-field">
                        <label for="heatPump8kW">8 kW (Kč):</label>
                        <input type="number" id="heatPump8kW" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="heatPump12kW">12 kW (Kč):</label>
                        <input type="number" id="heatPump12kW" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="heatPump16kW">16 kW (Kč):</label>
                        <input type="number" id="heatPump16kW" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="heatPump22kW">22 kW (Kč):</label>
                        <input type="number" id="heatPump22kW" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="heatPumpSubsidyHeatingOnly">Dotace - pouze vytápění (Kč):</label>
                        <input type="number" id="heatPumpSubsidyHeatingOnly" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="heatPumpSubsidyHeatingWater">Dotace - vytápění + ohřev vody (Kč):</label>
                        <input type="number" id="heatPumpSubsidyHeatingWater" min="0" step="1">
                    </div>
                </div>
            </div>

            <!-- Photovoltaic Water Heating -->
            <div class="pricing-section">
                <h3>Příprava teplé vody – fotovoltaický ohřev vody</h3>
                <div class="pricing-grid">
                    <div class="pricing-field">
                        <label for="photovoltaicSlopedRoof">Šikmá střecha (Kč):</label>
                        <input type="number" id="photovoltaicSlopedRoof" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="photovoltaicFlatRoof">Rovná střecha (Kč):</label>
                        <input type="number" id="photovoltaicFlatRoof" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="photovoltaicNewBoiler">Nový bojler (Kč):</label>
                        <input type="number" id="photovoltaicNewBoiler" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="photovoltaicSubsidy">Dotace (Kč):</label>
                        <input type="number" id="photovoltaicSubsidy" min="0" step="1">
                    </div>
                </div>
            </div>

            <!-- FVE Battery System -->
            <div class="pricing-section">
                <h3>Fotovoltaické systémy pro výrobu el. energie</h3>
                <div class="pricing-grid">
                    <div class="pricing-field">
                        <label for="fveSystem5_4kWp">5,4 kWp + 6,2 kWh (Kč):</label>
                        <input type="number" id="fveSystem5_4kWp" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="fveSystem7_2kWp">7,2 kWp + 9,3 kWh (Kč):</label>
                        <input type="number" id="fveSystem7_2kWp" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="fveSystem9_9kWp">9,9 kWp + 11,6 kWh (Kč):</label>
                        <input type="number" id="fveSystem9_9kWp" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="fveSystem14_4kWp">14,4 kWp + 15,5 kWh (Kč):</label>
                        <input type="number" id="fveSystem14_4kWp" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="fveWallboxCost">Wallbox - cena za kus (Kč):</label>
                        <input type="number" id="fveWallboxCost" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="fveBaseSubsidy">Základní dotace FVE (Kč):</label>
                        <input type="number" id="fveBaseSubsidy" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="fveGridSubsidy">Dotace připojení do sítě (Kč):</label>
                        <input type="number" id="fveGridSubsidy" min="0" step="1">
                    </div>
                </div>
            </div>

            <!-- Bonuses -->
            <div class="pricing-section">
                <h3>Bonusy a dotace</h3>
                <div class="pricing-grid">
                    <div class="pricing-field">
                        <label for="regionalBonusPercent">Regionální bonus (%):</label>
                        <input type="number" id="regionalBonusPercent" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="childFullCare">Dítě v plné péči (Kč):</label>
                        <input type="number" id="childFullCare" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="childPartialCare">Dítě ve střídavé péči (Kč):</label>
                        <input type="number" id="childPartialCare" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="combinationInsulationFVE">Zateplení + FVE (Kč):</label>
                        <input type="number" id="combinationInsulationFVE" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="combinationInsulationHeatSource">Zateplení + zdroj tepla (Kč):</label>
                        <input type="number" id="combinationInsulationHeatSource" min="0" step="1">
                    </div>
                    <div class="pricing-field">
                        <label for="basicSupport">Základní podpora (Kč):</label>
                        <input type="number" id="basicSupport" min="0" step="1">
                    </div>
                </div>
            </div>

            <div class="action-buttons">
                <button id="savePricing" class="btn btn-primary">
                    Uložit ceny
                </button>
                <button id="resetPricing" class="btn btn-danger">
                    Resetovat na základní ceny
                </button>
                <button id="updateDefaults" class="btn btn-warning">
                    Přepsat nové základní hodnoty
                </button>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            loadPricingConfiguration();

            $('#savePricing').click(savePricingConfiguration);
            $('#resetPricing').click(resetPricingConfiguration);
            $('#updateDefaults').click(updateDefaultValues);
        });

        function loadPricingConfiguration() {
            $.ajax({
                url: '/pricing/get',
                type: 'GET',
                success: function(data) {
                    populateForm(data);
                    $('#loadingIndicator').hide();
                    $('#pricingForm').show();
                },
                error: function(xhr, status, error) {
                    console.error('Error loading pricing configuration:', error);
                    showAlert('Failed to load pricing configuration. Please try again.', 'danger');
                    $('#loadingIndicator').hide();
                }
            });
        }

        function populateForm(pricing) {
            // Facade
            $('#facadePolystyrene').val(pricing.facade?.polystyrene || 2320);
            $('#facadeMineralWool').val(pricing.facade?.mineralWool || 2680);
            $('#facadeRevealCost').val(pricing.facade?.revealCost || 215);
            $('#facadeSubsidy').val(pricing.facade?.subsidy || 1300);

            // Roof
            $('#roofWithoutCladding').val(pricing.roof?.withoutCladding || 1650);
            $('#roofWithCladding').val(pricing.roof?.withCladding || 2750);
            $('#roofSubsidy').val(pricing.roof?.subsidy || 1300);

            // Attic
            $('#atticBaseCost').val(pricing.attic?.baseCost || 775);
            $('#atticWithFloorRemoval').val(pricing.attic?.withFloorRemoval || 1025);
            $('#atticOsbBoardCost').val(pricing.attic?.osbBoardCost || 1195);
            $('#atticWalkwayCost').val(pricing.attic?.walkwayCost || 850);
            $('#atticSubsidy').val(pricing.attic?.subsidy || 500);

            // Wall
            $('#wallBaseCost').val(pricing.wall?.baseCost || 1250);
            $('#wallSubsidy').val(pricing.wall?.subsidy || 500);

            // Floor
            $('#floorBaseCost').val(pricing.floor?.baseCost || 2200);
            $('#floorSubsidy').val(pricing.floor?.subsidy || 1700);

            // Windows
            $('#windowsBaseCost').val(pricing.windows?.baseCost || 6700);
            $('#windowsSubsidy').val(pricing.windows?.subsidy || 4900);

            // Shading
            $('#shadingAreaCost').val(pricing.shading?.areaCost || 2900);
            $('#shadingWindowWidthCost').val(pricing.shading?.windowWidthCost || 3000);
            $('#shadingSubsidy').val(pricing.shading?.subsidy || 1500);

            // Heat Pump
            $('#heatPump8kW').val(pricing.heatPump?.power8kW || 225000);
            $('#heatPump12kW').val(pricing.heatPump?.power12kW || 260000);
            $('#heatPump16kW').val(pricing.heatPump?.power16kW || 285000);
            $('#heatPump22kW').val(pricing.heatPump?.power22kW || 310000);
            $('#heatPumpSubsidyHeatingOnly').val(pricing.heatPump?.subsidyHeatingOnly || 75000);
            $('#heatPumpSubsidyHeatingWater').val(pricing.heatPump?.subsidyHeatingWater || 90000);

            // Photovoltaic Water Heating
            $('#photovoltaicSlopedRoof').val(pricing.photovoltaic?.slopedRoof || 72300);
            $('#photovoltaicFlatRoof').val(pricing.photovoltaic?.flatRoof || 78200);
            $('#photovoltaicNewBoiler').val(pricing.photovoltaic?.newBoiler || 12100);
            $('#photovoltaicSubsidy').val(pricing.photovoltaic?.subsidy || 35000);

            // Heat Recovery
            $('#heatRecoveryBaseCost').val(pricing.heatRecovery?.baseCost || 210000);
            $('#heatRecoverySubsidy').val(pricing.heatRecovery?.subsidy || 90000);

            // Rainwater
            $('#rainwaterBaseCost').val(pricing.rainwater?.baseCost || 45000);
            $('#rainwaterCostPerM3').val(pricing.rainwater?.costPerM3 || 9000);
            $('#rainwaterBaseSubsidy').val(pricing.rainwater?.baseSubsidy || 20000);
            $('#rainwaterSubsidyPerM3').val(pricing.rainwater?.subsidyPerM3 || 3000);

            // FVE Battery System
            $('#fveSystem5_4kWp').val(pricing.fveBattery?.system5_4kWp || 260000);
            $('#fveSystem7_2kWp').val(pricing.fveBattery?.system7_2kWp || 300000);
            $('#fveSystem9_9kWp').val(pricing.fveBattery?.system9_9kWp || 320000);
            $('#fveSystem14_4kWp').val(pricing.fveBattery?.system14_4kWp || 410000);
            $('#fveWallboxCost').val(pricing.fveBattery?.wallboxCost || 15000);
            $('#fveBaseSubsidy').val(pricing.fveBattery?.baseSubsidy || 100000);
            $('#fveGridSubsidy').val(pricing.fveBattery?.gridSubsidy || 40000);

            // Bonuses
            $('#regionalBonusPercent').val(pricing.bonuses?.regionalBonusPercent || 5);
            $('#childFullCare').val(pricing.bonuses?.childFullCare || 50000);
            $('#childPartialCare').val(pricing.bonuses?.childPartialCare || 25000);
            $('#combinationInsulationFVE').val(pricing.bonuses?.combinationInsulationFVE || 50000);
            $('#combinationInsulationHeatSource').val(pricing.bonuses?.combinationInsulationHeatSource || 50000);
            $('#basicSupport').val(pricing.bonuses?.basicSupport || 50000);
        }

        function savePricingConfiguration() {
            const pricingData = {
                facade: {
                    polystyrene: parseInt($('#facadePolystyrene').val()) || 2320,
                    mineralWool: parseInt($('#facadeMineralWool').val()) || 2680,
                    revealCost: parseInt($('#facadeRevealCost').val()) || 215,
                    subsidy: parseInt($('#facadeSubsidy').val()) || 1300
                },
                roof: {
                    withoutCladding: parseInt($('#roofWithoutCladding').val()) || 1650,
                    withCladding: parseInt($('#roofWithCladding').val()) || 2750,
                    subsidy: parseInt($('#roofSubsidy').val()) || 1300
                },
                attic: {
                    baseCost: parseInt($('#atticBaseCost').val()) || 775,
                    withFloorRemoval: parseInt($('#atticWithFloorRemoval').val()) || 1025,
                    osbBoardCost: parseInt($('#atticOsbBoardCost').val()) || 1195,
                    walkwayCost: parseInt($('#atticWalkwayCost').val()) || 850,
                    subsidy: parseInt($('#atticSubsidy').val()) || 500
                },
                wall: {
                    baseCost: parseInt($('#wallBaseCost').val()) || 1250,
                    subsidy: parseInt($('#wallSubsidy').val()) || 500
                },
                floor: {
                    baseCost: parseInt($('#floorBaseCost').val()) || 2200,
                    subsidy: parseInt($('#floorSubsidy').val()) || 1700
                },
                windows: {
                    baseCost: parseInt($('#windowsBaseCost').val()) || 6700,
                    subsidy: parseInt($('#windowsSubsidy').val()) || 4900
                },
                shading: {
                    areaCost: parseInt($('#shadingAreaCost').val()) || 2900,
                    windowWidthCost: parseInt($('#shadingWindowWidthCost').val()) || 3000,
                    subsidy: parseInt($('#shadingSubsidy').val()) || 1500
                },
                heatPump: {
                    power8kW: parseInt($('#heatPump8kW').val()) || 225000,
                    power12kW: parseInt($('#heatPump12kW').val()) || 260000,
                    power16kW: parseInt($('#heatPump16kW').val()) || 285000,
                    power22kW: parseInt($('#heatPump22kW').val()) || 310000,
                    subsidyHeatingOnly: parseInt($('#heatPumpSubsidyHeatingOnly').val()) || 75000,
                    subsidyHeatingWater: parseInt($('#heatPumpSubsidyHeatingWater').val()) || 90000
                },
                photovoltaic: {
                    slopedRoof: parseInt($('#photovoltaicSlopedRoof').val()) || 72300,
                    flatRoof: parseInt($('#photovoltaicFlatRoof').val()) || 78200,
                    newBoiler: parseInt($('#photovoltaicNewBoiler').val()) || 12100,
                    subsidy: parseInt($('#photovoltaicSubsidy').val()) || 35000
                },
                heatRecovery: {
                    baseCost: parseInt($('#heatRecoveryBaseCost').val()) || 210000,
                    subsidy: parseInt($('#heatRecoverySubsidy').val()) || 90000
                },
                rainwater: {
                    baseCost: parseInt($('#rainwaterBaseCost').val()) || 45000,
                    costPerM3: parseInt($('#rainwaterCostPerM3').val()) || 9000,
                    baseSubsidy: parseInt($('#rainwaterBaseSubsidy').val()) || 20000,
                    subsidyPerM3: parseInt($('#rainwaterSubsidyPerM3').val()) || 3000
                },
                fveBattery: {
                    system5_4kWp: parseInt($('#fveSystem5_4kWp').val()) || 260000,
                    system7_2kWp: parseInt($('#fveSystem7_2kWp').val()) || 300000,
                    system9_9kWp: parseInt($('#fveSystem9_9kWp').val()) || 320000,
                    system14_4kWp: parseInt($('#fveSystem14_4kWp').val()) || 410000,
                    wallboxCost: parseInt($('#fveWallboxCost').val()) || 15000,
                    baseSubsidy: parseInt($('#fveBaseSubsidy').val()) || 100000,
                    gridSubsidy: parseInt($('#fveGridSubsidy').val()) || 40000
                },
                bonuses: {
                    regionalBonusPercent: parseInt($('#regionalBonusPercent').val()) || 5,
                    childFullCare: parseInt($('#childFullCare').val()) || 50000,
                    childPartialCare: parseInt($('#childPartialCare').val()) || 25000,
                    combinationInsulationFVE: parseInt($('#combinationInsulationFVE').val()) || 50000,
                    combinationInsulationHeatSource: parseInt($('#combinationInsulationHeatSource').val()) || 50000,
                    basicSupport: parseInt($('#basicSupport').val()) || 50000
                }
            };

            $('#savePricing').text('Ukládání...').prop('disabled', true);

            $.ajax({
                url: '/pricing/update',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(pricingData),
                success: function(data) {
                    showAlert('Pricing configuration saved successfully!', 'success');
                    $('#savePricing').text('Uložit ceny').prop('disabled', false);
                },
                error: function(xhr, status, error) {
                    console.error('Error saving pricing configuration:', error);
                    showAlert('Failed to save pricing configuration. Please try again.', 'danger');
                    $('#savePricing').text('Uložit ceny').prop('disabled', false);
                }
            });
        }

        function resetPricingConfiguration() {
            if (!confirm('Are you sure you want to reset all pricing to default values? This action cannot be undone.')) {
                return;
            }

            $('#resetPricing').text('Resetování...').prop('disabled', true);

            $.ajax({
                url: '/pricing/reset',
                type: 'POST',
                success: function(data) {
                    showAlert('Pricing configuration reset to defaults!', 'success');
                    $('#resetPricing').text('Resetovat na základní ceny').prop('disabled', false);
                    loadPricingConfiguration(); // Reload the form with default values
                },
                error: function(xhr, status, error) {
                    console.error('Error resetting pricing configuration:', error);
                    showAlert('Failed to reset pricing configuration. Please try again.', 'danger');
                    $('#resetPricing').text('Resetovat na základní ceny').prop('disabled', false);
                }
            });
        }

        function updateDefaultValues() {
            console.log('updateDefaultValues function called');

            if (!confirm('Opravdu chcete přepsat základní hodnoty aktuálními hodnotami? Tato akce nemůže být vrácena zpět a ovlivní všechny budoucí resetování.')) {
                console.log('User cancelled the operation');
                return;
            }

            console.log('User confirmed, proceeding with update...');

            // Collect current form values (same as savePricingConfiguration)
            const pricingData = {
                facade: {
                    polystyrene: parseInt($('#facadePolystyrene').val()) || 2320,
                    mineralWool: parseInt($('#facadeMineralWool').val()) || 2680,
                    revealCost: parseInt($('#facadeRevealCost').val()) || 215,
                    subsidy: parseInt($('#facadeSubsidy').val()) || 1300
                },
                roof: {
                    withoutCladding: parseInt($('#roofWithoutCladding').val()) || 1650,
                    withCladding: parseInt($('#roofWithCladding').val()) || 2750,
                    subsidy: parseInt($('#roofSubsidy').val()) || 1300
                },
                attic: {
                    baseCost: parseInt($('#atticBaseCost').val()) || 775,
                    withFloorRemoval: parseInt($('#atticWithFloorRemoval').val()) || 1025,
                    osbBoardCost: parseInt($('#atticOsbBoardCost').val()) || 1195,
                    walkwayCost: parseInt($('#atticWalkwayCost').val()) || 850,
                    subsidy: parseInt($('#atticSubsidy').val()) || 500
                },
                wall: {
                    baseCost: parseInt($('#wallBaseCost').val()) || 1250,
                    subsidy: parseInt($('#wallSubsidy').val()) || 500
                },
                floor: {
                    baseCost: parseInt($('#floorBaseCost').val()) || 2200,
                    subsidy: parseInt($('#floorSubsidy').val()) || 1700
                },
                windows: {
                    baseCost: parseInt($('#windowsBaseCost').val()) || 6700,
                    subsidy: parseInt($('#windowsSubsidy').val()) || 4900
                },
                shading: {
                    areaCost: parseInt($('#shadingAreaCost').val()) || 2900,
                    windowWidthCost: parseInt($('#shadingWindowWidthCost').val()) || 3000,
                    subsidy: parseInt($('#shadingSubsidy').val()) || 1500
                },
                heatPump: {
                    power8kW: parseInt($('#heatPump8kW').val()) || 225000,
                    power12kW: parseInt($('#heatPump12kW').val()) || 260000,
                    power16kW: parseInt($('#heatPump16kW').val()) || 285000,
                    power22kW: parseInt($('#heatPump22kW').val()) || 310000,
                    subsidyHeatingOnly: parseInt($('#heatPumpSubsidyHeatingOnly').val()) || 75000,
                    subsidyHeatingWater: parseInt($('#heatPumpSubsidyHeatingWater').val()) || 90000
                },
                photovoltaic: {
                    slopedRoof: parseInt($('#photovoltaicSlopedRoof').val()) || 72300,
                    flatRoof: parseInt($('#photovoltaicFlatRoof').val()) || 78200,
                    newBoiler: parseInt($('#photovoltaicNewBoiler').val()) || 12100,
                    subsidy: parseInt($('#photovoltaicSubsidy').val()) || 35000
                },
                heatRecovery: {
                    baseCost: parseInt($('#heatRecoveryBaseCost').val()) || 210000,
                    subsidy: parseInt($('#heatRecoverySubsidy').val()) || 90000
                },
                rainwater: {
                    baseCost: parseInt($('#rainwaterBaseCost').val()) || 45000,
                    costPerM3: parseInt($('#rainwaterCostPerM3').val()) || 9000,
                    baseSubsidy: parseInt($('#rainwaterBaseSubsidy').val()) || 20000,
                    subsidyPerM3: parseInt($('#rainwaterSubsidyPerM3').val()) || 3000
                },
                fveBattery: {
                    system5_4kWp: parseInt($('#fveSystem5_4kWp').val()) || 260000,
                    system7_2kWp: parseInt($('#fveSystem7_2kWp').val()) || 300000,
                    system9_9kWp: parseInt($('#fveSystem9_9kWp').val()) || 320000,
                    system14_4kWp: parseInt($('#fveSystem14_4kWp').val()) || 410000,
                    wallboxCost: parseInt($('#fveWallboxCost').val()) || 15000,
                    baseSubsidy: parseInt($('#fveBaseSubsidy').val()) || 100000,
                    gridSubsidy: parseInt($('#fveGridSubsidy').val()) || 40000
                },
                bonuses: {
                    regionalBonusPercent: parseInt($('#regionalBonusPercent').val()) || 5,
                    childFullCare: parseInt($('#childFullCare').val()) || 50000,
                    childPartialCare: parseInt($('#childPartialCare').val()) || 25000,
                    combinationInsulationFVE: parseInt($('#combinationInsulationFVE').val()) || 50000,
                    combinationInsulationHeatSource: parseInt($('#combinationInsulationHeatSource').val()) || 50000,
                    basicSupport: parseInt($('#basicSupport').val()) || 50000
                }
            };

            console.log('Sending pricing data:', pricingData);
            $('#updateDefaults').text('Přepisování...').prop('disabled', true);

            $.ajax({
                url: '/pricing/update-defaults',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(pricingData),
                success: function(data) {
                    console.log('Success response:', data);
                    showAlert('Základní hodnoty byly úspěšně přepsány! Tyto hodnoty se nyní použijí při resetování.', 'success');
                    $('#updateDefaults').text('Přepsat nové základní hodnoty').prop('disabled', false);
                },
                error: function(xhr, status, error) {
                    console.error('Error updating default values:', error);
                    console.error('XHR:', xhr);
                    console.error('Status:', status);
                    showAlert('Nepodařilo se přepsat základní hodnoty. Zkuste to prosím znovu.', 'danger');
                    $('#updateDefaults').text('Přepsat nové základní hodnoty').prop('disabled', false);
                }
            });
        }

        function showAlert(message, type) {
            const alertHtml = `
                <div class="alert alert-${type}">
                    ${message}
                </div>
            `;
            $('#alertContainer').html(alertHtml).find('.alert').show();

            setTimeout(() => {
                $('.alert').fadeOut();
            }, 5000);
        }
    </script>
</body>
</html>
