// Test the new EmailService with SMTP + API fallback
const dotenv = require('dotenv');
const emailService = require('./services/emailService');

// Load environment variables
dotenv.config();

console.log('Testing EmailService with SMTP + API fallback...');
console.log('Environment:', process.env.NODE_ENV || 'development');
console.log('SMTP User configured:', !!process.env.BREVO_SMTP_USER);
console.log('SMTP Pass configured:', !!process.env.BREVO_SMTP_PASS);
console.log('Brevo API Key configured:', !!process.env.BREVO_API_KEY);

async function testEmailService() {
    try {
        console.log('\n=== Testing EmailService Verification ===');
        await emailService.verify();
        console.log('✅ EmailService verification successful!');
        
        console.log('\n=== Testing Email Sending ===');
        const testMailOptions = {
            to: '<EMAIL>',
            subject: 'EmailService Test - ' + new Date().toISOString(),
            text: 'This is a test email from the new EmailService with SMTP + API fallback.',
            html: '<p>This is a test email from the new <strong>EmailService</strong> with SMTP + API fallback.</p><p>If you receive this, the fallback system is working!</p>'
        };
        
        const result = await emailService.sendEmail(testMailOptions);
        console.log('✅ Test email sent successfully!');
        console.log('Result:', {
            messageId: result.messageId,
            response: result.response,
            accepted: result.accepted,
            rejected: result.rejected
        });
        
        return true;
    } catch (error) {
        console.error('❌ EmailService test failed:');
        console.error('Error type:', error.constructor.name);
        console.error('Error message:', error.message);
        
        if (error.stack) {
            console.error('Stack trace:', error.stack);
        }
        
        return false;
    }
}

// Run the test
testEmailService().then(success => {
    if (success) {
        console.log('\n🎉 EmailService test completed successfully!');
        console.log('The system will automatically fallback from SMTP to API when needed.');
        process.exit(0);
    } else {
        console.log('\n💥 EmailService test failed!');
        console.log('Please check your Brevo SMTP credentials and API key.');
        process.exit(1);
    }
}).catch(error => {
    console.error('\n💥 Unexpected error during EmailService test:', error);
    process.exit(1);
});
