const express = require('express');
const passport = require('passport');
const router = express.Router();

// Google Auth Routes
router.get('/google', passport.authenticate('google', {
    scope: ['profile', 'email'],
    prompt: 'select_account'  // Force account selection screen
}));

router.get('/google/callback',
  passport.authenticate('google', { failureRedirect: '/login', failureFlash: false }),
  (req, res) => {
    // Successful authentication
    res.redirect('/dashboard');
  }
);

// Debug route to check authentication status
router.get('/status', (req, res) => {
  res.json({
    authenticated: req.isAuthenticated(),
    user: req.user ? {
      id: req.user.id,
      displayName: req.user.displayName,
      email: req.user.email,
      profileImage: req.user.profileImage,
      role: req.user.role
    } : null
  });
});

// Debug route to check OAuth configuration
router.get('/debug-oauth', (req, res) => {
  const callbackURL = process.env.GOOGLE_CALLBACK_URL || (process.env.NODE_ENV === 'production'
    ? 'https://kalkulacka.evgroup.cz/auth/google/callback'
    : 'http://localhost:3000/auth/google/callback');

  res.json({
    NODE_ENV: process.env.NODE_ENV,
    GOOGLE_CALLBACK_URL_ENV: process.env.GOOGLE_CALLBACK_URL,
    GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID ? 'SET' : 'NOT SET',
    GOOGLE_CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET ? 'SET' : 'NOT SET',
    finalCallbackURL: callbackURL,
    currentDomain: req.get('host'),
    protocol: req.protocol,
    fullUrl: `${req.protocol}://${req.get('host')}${req.originalUrl}`
  });
});

// Logout route
router.get('/logout', (req, res, next) => {
  req.logout(function(err) {
    if (err) { return next(err); }
    res.redirect('/');
  });
});

module.exports = router;
