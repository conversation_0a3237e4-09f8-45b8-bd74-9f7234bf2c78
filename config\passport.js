const passport = require('passport');
const GoogleStrategy = require('passport-google-oauth20').Strategy;
const { admin, db } = require('../firebase-admin');
const fs = require('fs');
const path = require('path');

// Load Google OAuth credentials from JSON file
let googleCredentials;
try {
  const credentialsPath = path.join(__dirname, '..', 'client_secret_270174619272-tv7ec3rpdu0vcsakb5sjarkemh2m811q.apps.googleusercontent.com.json');
  const credentialsFile = fs.readFileSync(credentialsPath, 'utf8');
  googleCredentials = JSON.parse(credentialsFile).web;
  console.log('Google OAuth credentials loaded from file');
} catch (error) {
  console.error('Error loading Google OAuth credentials:', error);
  // Fallback to environment variables
  googleCredentials = {
    client_id: process.env.GOOGLE_CLIENT_ID,
    client_secret: process.env.GOOGLE_CLIENT_SECRET
  };
}

const callbackURL = process.env.GOOGLE_CALLBACK_URL || (process.env.NODE_ENV === 'production'
  ? 'https://kalkulacka.evgroup.cz/auth/google/callback'
  : 'http://localhost:3000/auth/google/callback');

console.log('OAuth Configuration:');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('Client ID:', googleCredentials.client_id ? 'Loaded' : 'Missing');
console.log('Final callback URL:', callbackURL);

passport.use(new GoogleStrategy({
    clientID: googleCredentials.client_id,
    clientSecret: googleCredentials.client_secret,
    callbackURL: callbackURL,
    scope: ['profile', 'email']
  },
  async (accessToken, refreshToken, profile, done) => {
    try {
      // Check if user already exists in Firestore
      const usersRef = db.collection('users');
      const snapshot = await usersRef.where('googleId', '==', profile.id).get();

      if (!snapshot.empty) {
        // User exists, return the user data
        const userData = snapshot.docs[0].data();
        const user = {
          id: snapshot.docs[0].id,
          ...userData
        };
        return done(null, user);
      }

      // Check if this is the first user (will be admin)
      const countSnapshot = await usersRef.count().get();
      const isFirstUser = countSnapshot.data().count === 0;

      // Fix Google profile image URL to use a more reliable format
      let profileImageUrl = profile.photos?.[0]?.value || '';
      if (profileImageUrl && profileImageUrl.includes('googleusercontent.com')) {
        // Remove size parameter and add a reliable size
        const baseUrl = profileImageUrl.split('=')[0];
        profileImageUrl = baseUrl + '=s96-c';
      }

      // If not, create a new user
      const newUser = {
        googleId: profile.id,
        displayName: profile.displayName || 'User',
        firstName: profile.name?.givenName || '',
        lastName: profile.name?.familyName || '',
        email: profile.emails?.[0]?.value || '',
        profileImage: profileImageUrl,
        // First user will be admin, others will be regular users
        role: isFirstUser ? 'admin' : 'user',
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      };

      // Add user to Firestore
      const docRef = await usersRef.add(newUser);
      const user = {
        id: docRef.id,
        ...newUser
      };

      console.log('New user created:', user.displayName);
      return done(null, user);
    } catch (err) {
      console.error('Error in Google strategy:', err);
      return done(err, null);
    }
  }
));

passport.serializeUser((user, done) => {
  done(null, user.id);
});

passport.deserializeUser(async (id, done) => {
  try {
    const userDoc = await db.collection('users').doc(id).get();

    if (!userDoc.exists) {
      return done(null, null);
    }

    const user = {
      id: userDoc.id,
      ...userDoc.data()
    };

    done(null, user);
  } catch (err) {
    console.error('Error deserializing user:', err);
    done(err, null);
  }
});
