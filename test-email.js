// Email testing script
const nodemailer = require('nodemailer');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

console.log('Testing email configuration...');
console.log('BREVO_SMTP_USER:', process.env.BREVO_SMTP_USER ? 'Set' : 'Not set');
console.log('BREVO_SMTP_PASS:', process.env.BREVO_SMTP_PASS ? 'Set' : 'Not set');

// Configure Brevo SMTP transporter
const transporter = nodemailer.createTransport({
    host: 'smtp-relay.brevo.com',
    port: 587,
    secure: false, // TLS
    auth: {
        user: process.env.BREVO_SMTP_USER,
        pass: process.env.BREVO_SMTP_PASS
    },
    debug: true, // Enable debug output
    logger: true // Log to console
});

async function testEmail() {
    try {
        console.log('Testing SMTP connection...');
        
        // Verify connection
        await transporter.verify();
        console.log('✅ SMTP connection successful!');
        
        // Send test email
        const testMailOptions = {
            from: '"Kalkulace Test" <<EMAIL>>',
            to: '<EMAIL>', // Send to yourself for testing
            subject: 'Test Email - Kalkulace App',
            text: 'This is a test email to verify the email configuration is working.',
            html: '<p>This is a test email to verify the email configuration is working.</p>'
        };
        
        console.log('Sending test email...');
        const result = await transporter.sendMail(testMailOptions);
        console.log('✅ Test email sent successfully!');
        console.log('Message ID:', result.messageId);
        console.log('Response:', result.response);
        
    } catch (error) {
        console.error('❌ Email test failed:');
        console.error('Error type:', error.constructor.name);
        console.error('Error message:', error.message);
        console.error('Error code:', error.code);
        console.error('Error response:', error.response);
        console.error('Error responseCode:', error.responseCode);
        console.error('Error command:', error.command);
        
        // Additional debugging
        if (error.code === 'EAUTH') {
            console.error('Authentication failed - check your BREVO credentials');
        } else if (error.code === 'ECONNECTION') {
            console.error('Connection failed - check your internet connection and SMTP settings');
        }
    }
}

// Run the test
testEmail().then(() => {
    console.log('Email test completed');
    process.exit(0);
}).catch((error) => {
    console.error('Unexpected error:', error);
    process.exit(1);
});
