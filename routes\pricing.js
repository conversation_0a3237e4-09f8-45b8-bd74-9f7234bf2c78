const express = require('express');
const router = express.Router();
const { ensureAuth, checkRole } = require('../middleware/auth');
const { admin, db } = require('../firebase-admin');

// Default pricing configuration
const defaultPricing = {
  facade: {
    polystyrene: 2320,
    mineralWool: 2680,
    revealCost: 215,
    subsidy: 1300
  },
  roof: {
    withoutCladding: 1650,
    withCladding: 2750,
    subsidy: 1300
  },
  attic: {
    baseCost: 775,
    withFloorRemoval: 1025,
    osbBoardCost: 1195,
    walkwayCost: 850,
    subsidy: 500
  },
  wall: {
    baseCost: 1250,
    subsidy: 500
  },
  floor: {
    baseCost: 2200,
    subsidy: 1700
  },
  windows: {
    baseCost: 6700,
    subsidy: 4900
  },
  shading: {
    areaCost: 2900,
    windowWidthCost: 3000,
    subsidy: 1500
  },
  heatPump: {
    power8kW: 225000,
    power12kW: 260000,
    power16kW: 285000,
    power22kW: 310000,
    subsidyHeatingOnly: 75000,
    subsidyHeatingWater: 90000
  },
  photovoltaic: {
    slopedRoof: 72300,
    flatRoof: 78200,
    newBoiler: 12100,
    subsidy: 35000
  },
  heatRecovery: {
    baseCost: 210000,
    subsidy: 90000
  },
  rainwater: {
    baseCost: 45000,
    costPerM3: 9000,
    baseSubsidy: 20000,
    subsidyPerM3: 3000
  },
  fveBattery: {
    system5_4kWp: 260000,  // 5,4 kWp + 6,2 kWh
    system7_2kWp: 300000,  // 7,2 kWp + 9,3 kWh
    system9_9kWp: 320000,  // 9,9 kWp + 11,6 kWh
    system14_4kWp: 410000, // 14,4 kWp + 15,5 kWh
    wallboxCost: 15000,    // Cost per wallbox
    baseSubsidy: 100000,   // Base subsidy for FVE system
    gridSubsidy: 40000     // Additional subsidy for grid connection
  },
  bonuses: {
    regionalBonusPercent: 5,
    childFullCare: 50000,
    childPartialCare: 25000,
    combinationInsulationFVE: 50000,      // Zateplení + FVE
    combinationInsulationHeatSource: 50000, // Zateplení + zdroj tepla
    basicSupport: 50000
  }
};

// Get pricing configuration
router.get('/get', ensureAuth, checkRole(['editor', 'admin']), async (req, res) => {
  try {
    console.log('Fetching pricing configuration...');
    
    const pricingDoc = await db.collection('settings').doc('pricing').get();
    
    if (!pricingDoc.exists) {
      console.log('No pricing configuration found, returning defaults');
      return res.json(defaultPricing);
    }
    
    const pricing = pricingDoc.data();
    console.log('Pricing configuration found:', pricing);
    
    res.json(pricing);
  } catch (error) {
    console.error('Error fetching pricing configuration:', error);
    res.status(500).json({ message: 'Failed to fetch pricing configuration' });
  }
});

// Input validation helper
function validatePricingData(data) {
  if (!data || typeof data !== 'object') {
    return { valid: false, error: 'Invalid pricing data structure' };
  }

  // Check for required sections
  const requiredSections = ['facade', 'roof', 'attic', 'wall', 'floor', 'windows', 'bonuses'];
  for (const section of requiredSections) {
    if (!data[section] || typeof data[section] !== 'object') {
      return { valid: false, error: `Missing or invalid section: ${section}` };
    }
  }

  // Validate numeric values
  function validateNumericValues(obj, path = '') {
    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === 'object' && value !== null) {
        const result = validateNumericValues(value, `${path}.${key}`);
        if (!result.valid) return result;
      } else if (typeof value === 'number') {
        if (value < 0 || value > 10000000) { // Reasonable limits
          return { valid: false, error: `Invalid value at ${path}.${key}: ${value}` };
        }
      }
    }
    return { valid: true };
  }

  return validateNumericValues(data);
}

// Update pricing configuration
router.post('/update', ensureAuth, checkRole(['editor', 'admin']), async (req, res) => {
  try {
    const pricingData = req.body;

    // Validate the pricing data structure
    const validation = validatePricingData(pricingData);
    if (!validation.valid) {
      return res.status(400).json({ message: validation.error });
    }
    
    // Add metadata
    const updatedPricing = {
      ...pricingData,
      updatedBy: req.user.id,
      updatedByName: req.user.displayName,
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    };
    
    // Save to Firestore
    await db.collection('settings').doc('pricing').set(updatedPricing, { merge: true });
    
    console.log('Pricing configuration updated by:', req.user.displayName);
    
    res.json({ success: true, message: 'Pricing configuration updated successfully' });
  } catch (error) {
    console.error('Error updating pricing configuration:', error);
    res.status(500).json({ message: 'Failed to update pricing configuration' });
  }
});

// Reset pricing to defaults
router.post('/reset', ensureAuth, checkRole(['admin']), async (req, res) => {
  try {
    // Try to get custom default values first, fallback to hardcoded defaults
    let defaultsToUse = defaultPricing;

    try {
      const customDefaultsDoc = await db.collection('settings').doc('defaultPricing').get();
      if (customDefaultsDoc.exists) {
        const customDefaults = customDefaultsDoc.data();
        // Remove metadata fields and use only the pricing data
        const { updatedBy, updatedByName, updatedAt, defaultsUpdatedAt, ...pricingData } = customDefaults;
        defaultsToUse = pricingData;
        console.log('Using custom default values for reset');
      } else {
        console.log('No custom defaults found, using hardcoded defaults');
      }
    } catch (defaultsError) {
      console.warn('Error loading custom defaults, using hardcoded defaults:', defaultsError);
    }

    const resetPricing = {
      ...defaultsToUse,
      updatedBy: req.user.id,
      updatedByName: req.user.displayName,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      resetAt: admin.firestore.FieldValue.serverTimestamp()
    };

    await db.collection('settings').doc('pricing').set(resetPricing);

    console.log('Pricing configuration reset to defaults by:', req.user.displayName);

    res.json({ success: true, message: 'Pricing configuration reset to defaults' });
  } catch (error) {
    console.error('Error resetting pricing configuration:', error);
    res.status(500).json({ message: 'Failed to reset pricing configuration' });
  }
});

// Update default pricing values
router.post('/update-defaults', ensureAuth, checkRole(['admin']), async (req, res) => {
  try {
    console.log('=== UPDATE DEFAULTS ROUTE HIT ===');
    console.log('User:', req.user.displayName);
    console.log('Request body:', req.body);

    const newDefaultPricing = req.body;

    // Validate the pricing data structure
    if (!newDefaultPricing || typeof newDefaultPricing !== 'object') {
      console.log('Invalid pricing data received');
      return res.status(400).json({ message: 'Invalid pricing data' });
    }

    // Store the new default values in a special collection
    const updatedDefaults = {
      ...newDefaultPricing,
      updatedBy: req.user.id,
      updatedByName: req.user.displayName,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      defaultsUpdatedAt: admin.firestore.FieldValue.serverTimestamp()
    };

    // Save new defaults to a special document
    await db.collection('settings').doc('defaultPricing').set(updatedDefaults);

    console.log('Default pricing values updated by:', req.user.displayName);
    console.log('New default values saved successfully');

    res.json({ success: true, message: 'Default pricing values updated successfully' });
  } catch (error) {
    console.error('Error updating default pricing values:', error);
    res.status(500).json({ message: 'Failed to update default pricing values' });
  }
});

module.exports = router;
