<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Submissions</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .success {
            color: green;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>Test Submissions</h1>
    <button id="fetchBtn">Fetch Submissions</button>
    <div id="status"></div>
    <pre id="result"></pre>

    <script>
        document.getElementById('fetchBtn').addEventListener('click', async () => {
            const statusEl = document.getElementById('status');
            const resultEl = document.getElementById('result');
            
            statusEl.innerHTML = 'Fetching submissions...';
            statusEl.className = '';
            
            try {
                const response = await fetch('/submissions/list');
                statusEl.innerHTML = `Response status: ${response.status}`;
                
                if (!response.ok) {
                    const text = await response.text();
                    statusEl.className = 'error';
                    resultEl.textContent = `Error: ${text}`;
                    return;
                }
                
                const data = await response.json();
                statusEl.innerHTML = `Success! Found ${data.length} submissions`;
                statusEl.className = 'success';
                resultEl.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                statusEl.innerHTML = `Error: ${error.message}`;
                statusEl.className = 'error';
                resultEl.textContent = error.stack;
            }
        });
    </script>
</body>
</html>
