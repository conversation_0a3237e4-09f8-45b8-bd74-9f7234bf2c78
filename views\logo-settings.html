<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logo - Kalkulačka App</title>
    <link rel="stylesheet" href="/style.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        .logo-settings-container {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 30px;
            margin-top: 20px;
        }

        .logo-preview {
            margin: 20px 0;
            padding: 20px;
            border: 2px dashed #d1d5db;
            border-radius: var(--border-radius);
            text-align: center;
            min-height: 150px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .logo-preview img {
            max-width: 300px;
            max-height: 150px;
            display: none;
            border: 1px solid #e5e7eb;
        }

        .logo-preview.has-logo img {
            display: block;
            margin: 0 auto;
        }

        .logo-preview.has-logo #noLogoText {
            display: none;
        }

        .logo-preview p {
            color: var(--text-light);
            margin: 10px 0;
        }

        .file-upload {
            position: relative;
            overflow: hidden;
            margin: 20px 0;
            text-align: center;
        }

        .file-upload input[type="file"] {
            position: absolute;
            top: 0;
            right: 0;
            margin: 0;
            padding: 0;
            font-size: 20px;
            cursor: pointer;
            opacity: 0;
            filter: alpha(opacity=0);
        }

        .upload-btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: var(--primary-color);
            color: white;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
        }

        .upload-btn:hover {
            background-color: var(--primary-color-dark);
            transform: translateY(-2px);
        }

        .settings-group {
            margin: 20px 0;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #d1d5db;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--primary-color);
        }

        input:focus + .slider {
            box-shadow: 0 0 1px var(--primary-color);
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .toggle-label {
            display: inline-block;
            vertical-align: middle;
            margin-left: 10px;
        }

        .save-btn {
            display: block;
            width: 100%;
            padding: 12px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            font-family: 'Poppins', sans-serif;
            font-size: 16px;
            cursor: pointer;
            transition: var(--transition);
            margin-top: 20px;
        }

        .save-btn:hover {
            background-color: var(--primary-color-dark);
            transform: translateY(-2px);
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: var(--border-radius);
        }

        .alert-success {
            background-color: #d1fae5;
            color: #065f46;
        }

        .alert-danger {
            background-color: #fee2e2;
            color: #991b1b;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="dashboard-header">
            <h1>Logo</h1>
            <a href="/dashboard" class="back-link">
                <i class="fas fa-arrow-left"></i> Zpět
            </a>
        </div>

        <div id="alertContainer"></div>

        <div class="logo-settings-container">
            <h2>PDF Logo Settings</h2>
            <p>Upload a logo to be displayed in the PDF documents. The logo should be in PNG format with a transparent background for best results.</p>

            <div class="logo-preview" id="logoPreview">
                <img src="" alt="Logo Preview" id="logoImage">
                <p id="noLogoText">No logo uploaded yet</p>
            </div>

            <div class="file-upload">
                <label for="logoFile" class="upload-btn">
                    <i class="fas fa-upload"></i> Choose Logo File
                </label>
                <input type="file" id="logoFile" accept="image/png" onchange="previewLogo(this)">
            </div>

            <div class="settings-group">
                <label class="toggle-switch">
                    <input type="checkbox" id="enableLogo" checked>
                    <span class="slider"></span>
                </label>
                <span class="toggle-label">Enable logo in PDF documents</span>
            </div>

            <button id="saveSettings" class="save-btn">Save Settings</button>
        </div>
    </div>

    <script>
        // Check if user is logged in and has editor or admin role
        $(document).ready(function() {
            // Fetch current logo settings
            fetchLogoSettings();

            // Save settings button click handler
            $('#saveSettings').click(function() {
                saveLogoSettings();
            });
        });

        // Preview the selected logo
        function previewLogo(input) {
            if (input.files && input.files[0]) {
                const file = input.files[0];

                // Check file type
                if (!file.type.match('image/png')) {
                    showAlert('Please select a PNG image file.', 'danger');
                    input.value = '';
                    return;
                }

                // Check file size (max 1MB)
                if (file.size > 1024 * 1024) {
                    showAlert('Logo file size should be less than 1MB.', 'danger');
                    input.value = '';
                    return;
                }

                const reader = new FileReader();

                reader.onload = function(e) {
                    $('#logoImage').attr('src', e.target.result);
                    $('#logoPreview').addClass('has-logo');
                    $('#noLogoText').hide();
                };

                reader.readAsDataURL(file);
            }
        }

        // Fetch current logo settings
        function fetchLogoSettings() {
            console.log('Fetching logo settings...');
            $.ajax({
                url: '/logo-settings/get',
                type: 'GET',
                success: function(data) {
                    console.log('Received logo settings:', data);
                    if (data.logoUrl) {
                        // Add timestamp to prevent caching
                        const logoUrl = data.logoUrl + '?t=' + new Date().getTime();
                        console.log('Setting logo image src to:', logoUrl);
                        $('#logoImage').attr('src', logoUrl);
                        $('#logoPreview').addClass('has-logo');
                        $('#noLogoText').hide();

                        // Verify the image loaded correctly
                        $('#logoImage').on('load', function() {
                            console.log('Logo image loaded successfully');
                        }).on('error', function() {
                            console.error('Failed to load logo image from URL:', logoUrl);
                            showAlert('Failed to load logo image. Please try uploading again.', 'danger');
                        });
                    } else {
                        console.log('No logo URL found in settings');
                        // Reset logo preview if no logo URL
                        $('#logoImage').attr('src', '');
                        $('#logoPreview').removeClass('has-logo');
                        $('#noLogoText').show();
                    }

                    $('#enableLogo').prop('checked', data.enableLogo !== false);
                },
                error: function(xhr, status, error) {
                    console.error('Error fetching logo settings:', error);
                    showAlert('Failed to load logo settings. Please try again.', 'danger');
                }
            });
        }

        // Save logo settings
        function saveLogoSettings() {
            const fileInput = document.getElementById('logoFile');
            const enableLogo = document.getElementById('enableLogo').checked;

            // Create form data
            const formData = new FormData();
            formData.append('enableLogo', enableLogo);

            if (fileInput.files.length > 0) {
                formData.append('logo', fileInput.files[0]);
            }

            // Show loading state
            $('#saveSettings').text('Saving...').prop('disabled', true);

            // Send to server
            $.ajax({
                url: '/logo-settings/upload',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(data) {
                    showAlert('Logo settings saved successfully!', 'success');
                    $('#saveSettings').text('Save Settings').prop('disabled', false);

                    // Reset file input
                    fileInput.value = '';

                    // Refresh logo settings to show the updated logo
                    fetchLogoSettings();
                },
                error: function(xhr, status, error) {
                    console.error('Error saving logo settings:', error);
                    showAlert('Failed to save logo settings. Please try again.', 'danger');
                    $('#saveSettings').text('Save Settings').prop('disabled', false);
                }
            });
        }

        // Show alert message
        function showAlert(message, type) {
            const alertContainer = $('#alertContainer');
            const alert = $(`<div class="alert alert-${type}">${message}</div>`);

            alertContainer.empty().append(alert);

            setTimeout(function() {
                alert.fadeOut(function() {
                    $(this).remove();
                });
            }, 3000);
        }
    </script>
</body>
</html>
