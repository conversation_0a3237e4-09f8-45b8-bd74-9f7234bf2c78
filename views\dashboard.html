<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Ka<PERSON><PERSON>č<PERSON> App</title>
    <link rel="stylesheet" href="/style.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        /* Dashboard styles */
        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .dashboard-header h1 {
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
            background-color: white;
            padding: 15px;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
        }

        .profile-image {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
        }

        .user-role {
            display: inline-block;
            padding: 3px 8px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 20px;
            font-size: 12px;
            margin-top: 5px;
        }

        .dashboard-menu {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .menu-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 30px;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            text-decoration: none;
            color: var(--text-color);
            transition: var(--transition);
            text-align: center;
        }

        .menu-item i {
            font-size: 36px;
            margin-bottom: 15px;
            color: var(--primary-color);
        }

        .menu-item span {
            font-weight: 500;
        }

        .menu-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .menu-item.logout {
            background-color: #f8fafc;
        }

        .menu-item.logout i {
            color: #dc3545;
        }

        @media (max-width: 768px) {
            .dashboard-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .user-info {
                width: 100%;
                margin-top: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="dashboard-header">
            <div class="user-info">
                <img id="profileImage" src="" alt="Profile" class="profile-image">
                <div>
                    <p id="userName"></p>
                    <p id="userEmail"></p>
                    <p class="user-role" id="userRole"></p>
                </div>
            </div>
        </div>

        <div class="dashboard-menu" id="dashboardMenu">
            <!-- Menu items will be added dynamically based on user role -->
        </div>
    </div>

    <script>
        // Fetch user data
        async function fetchUserData() {
            try {
                const response = await fetch('/api/user');
                if (!response.ok) {
                    throw new Error('Failed to fetch user data');
                }

                const userData = await response.json();
                displayUserData(userData);
                createMenuItems(userData.role);
            } catch (error) {
                console.error('Error:', error);
            }
        }

        // Helper function to get reliable profile image URL
        function getReliableProfileImage(profileImage, displayName) {
            if (!profileImage || profileImage.trim() === '') {
                return getInitialsAvatar(displayName || 'User');
            }

            // Fix Google profile image URLs
            if (profileImage.includes('googleusercontent.com')) {
                const baseUrl = profileImage.split('=')[0];
                return baseUrl + '=s96-c';
            }

            return profileImage;
        }

        // Helper function to generate initials avatar
        function getInitialsAvatar(name) {
            const initials = (name || 'User').split(' ').map(n => n[0]).join('').substring(0, 2).toUpperCase();
            return `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&background=007bff&color=fff&size=96&bold=true`;
        }

        // Display user data
        function displayUserData(user) {
            document.getElementById('userName').textContent = user.displayName;
            document.getElementById('userEmail').textContent = user.email;
            document.getElementById('userRole').textContent = `Role: ${user.role.charAt(0).toUpperCase() + user.role.slice(1)}`;

            const profileImg = document.getElementById('profileImage');
            const reliableImageUrl = getReliableProfileImage(user.profileImage, user.displayName);
            const fallbackUrl = getInitialsAvatar(user.displayName || 'User');

            profileImg.src = reliableImageUrl;
            profileImg.onerror = function() {
                this.src = fallbackUrl;
                this.onerror = null; // Prevent infinite loop
            };
        }

        // Create menu items based on user role
        function createMenuItems(role) {
            const dashboardMenu = document.getElementById('dashboardMenu');

            // Calculator - available to all
            dashboardMenu.innerHTML = `
                <a href="/" class="menu-item">
                    <i class="fas fa-calculator"></i>
                    <span>Kalkulačka</span>
                </a>
            `;

            // Only editors and admins can access submissions and settings
            if (role === 'editor' || role === 'admin') {
                dashboardMenu.innerHTML += `
                    <a href="/submissions" class="menu-item">
                        <i class="fas fa-clipboard-list"></i>
                        <span>Správa nabídek</span>
                    </a>
                    <a href="/logo-settings" class="menu-item">
                        <i class="fas fa-image"></i>
                        <span>Logo</span>
                    </a>
                    <a href="/pricing-settings" class="menu-item">
                        <i class="fas fa-dollar-sign"></i>
                        <span>Úprava hodnot</span>
                    </a>
                `;
            }

            // Only Admin can access user management
            if (role === 'admin') {
                dashboardMenu.innerHTML += `
                    <a href="/admin" class="menu-item">
                        <i class="fas fa-users-cog"></i>
                        <span>Správa uživatelů</span>
                    </a>
                `;
            }

            // Logout - available to all
            dashboardMenu.innerHTML += `
                <a href="/auth/logout" class="menu-item logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            `;
        }

        // Load user data when page loads
        window.addEventListener('DOMContentLoaded', fetchUserData);
    </script>
</body>
</html>
