const express = require('express');
const router = express.Router();
const { ensureAuth, checkRole } = require('../middleware/auth');
const { admin, db } = require('../firebase-admin');
const { generateTemplatePDF } = require('../template-pdf');
const documentService = require('../services/documentService');

// Test endpoint to check authentication
router.get('/test-auth', ensureAuth, (req, res) => {
  console.log('Test auth endpoint hit - user:', req.user);
  res.json({
    authenticated: true,
    user: req.user,
    message: 'Authentication working'
  });
});

// Simple test to check database connection
router.get('/test-db', ensureAuth, checkRole(['admin', 'editor']), async (req, res) => {
  try {
    console.log('=== TESTING DATABASE CONNECTION ===');
    const snapshot = await db.collection('submissions').limit(1).get();
    console.log('Database query successful, found', snapshot.size, 'documents');
    res.json({
      message: 'Database connection working',
      count: snapshot.size
    });
  } catch (error) {
    console.error('Database test error:', error);
    res.status(500).json({ message: 'Database error', error: error.message });
  }
});

// Get all submissions with pagination, filtering, and search (API endpoint)
router.get('/list', ensureAuth, checkRole(['admin', 'editor']), async (req, res) => {
  console.log('=== SUBMISSIONS LIST ENDPOINT HIT ===');
  console.log('Query parameters:', req.query);
  console.log('User:', req.user.displayName);

  try {
    console.log('=== ENTERING TRY BLOCK ===');

    // Parse query parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 25;
    const status = req.query.status || 'all';
    const search = req.query.search || '';
    const dateFrom = req.query.dateFrom;
    const dateTo = req.query.dateTo;

    console.log('Parsed filters:', { page, limit, status, search, dateFrom, dateTo });

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Build base query - get all submissions and filter client-side to avoid Firestore index requirements
    let query = db.collection('submissions').orderBy('createdAt', 'desc');

    console.log('Using client-side filtering for all filters to avoid Firestore index requirements');

    // Get total count for pagination (before applying limit)
    console.log('Executing database query...');

    // Add timeout to the query
    const queryPromise = query.get();
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Database query timeout')), 10000)
    );

    const totalSnapshot = await Promise.race([queryPromise, timeoutPromise]);
    let allSubmissions = [];

    totalSnapshot.forEach(doc => {
      const data = doc.data();
      allSubmissions.push({
        id: doc.id,
        ...data,
        createdAt: data.createdAt ? data.createdAt.toDate() : new Date()
      });
    });

    console.log(`Found ${allSubmissions.length} submissions before client-side filtering`);

    // Debug: Log status values found in database
    const statusCounts = {};
    allSubmissions.forEach(submission => {
      const submissionStatus = submission.status || 'undefined';
      statusCounts[submissionStatus] = (statusCounts[submissionStatus] || 0) + 1;
    });
    console.log('Status distribution in database:', statusCounts);

    // Apply client-side status filter
    if (status !== 'all') {
      allSubmissions = allSubmissions.filter(submission => {
        return submission.status === status;
      });
      console.log(`Found ${allSubmissions.length} submissions after status filter (${status})`);
    }

    // Apply client-side date range filter
    if (dateFrom) {
      const fromDate = new Date(dateFrom);
      fromDate.setHours(0, 0, 0, 0);
      allSubmissions = allSubmissions.filter(submission => {
        const submissionDate = submission.createdAt;
        return submissionDate >= fromDate;
      });
      console.log(`Found ${allSubmissions.length} submissions after date from filter`);
    }

    if (dateTo) {
      const toDate = new Date(dateTo);
      toDate.setHours(23, 59, 59, 999);
      allSubmissions = allSubmissions.filter(submission => {
        const submissionDate = submission.createdAt;
        return submissionDate <= toDate;
      });
      console.log(`Found ${allSubmissions.length} submissions after date to filter`);
    }

    // Apply search filter on client side (since Firestore doesn't support full-text search)
    if (search) {
      const searchLower = search.toLowerCase();
      allSubmissions = allSubmissions.filter(submission => {
        return (
          (submission.name && submission.name.toLowerCase().includes(searchLower)) ||
          (submission.email && submission.email.toLowerCase().includes(searchLower)) ||
          (submission.phone && submission.phone.toLowerCase().includes(searchLower)) ||
          (submission.address && submission.address.toLowerCase().includes(searchLower)) ||
          (submission.userDisplayName && submission.userDisplayName.toLowerCase().includes(searchLower)) ||
          (submission.userEmail && submission.userEmail.toLowerCase().includes(searchLower))
        );
      });
      console.log(`Found ${allSubmissions.length} submissions after search filter`);
    }

    // Calculate pagination info
    const totalCount = allSubmissions.length;
    const totalPages = Math.ceil(totalCount / limit);

    // Apply pagination
    const paginatedSubmissions = allSubmissions.slice(offset, offset + limit);

    console.log(`Returning ${paginatedSubmissions.length} of ${totalCount} submissions (page ${page}/${totalPages})`);

    res.json({
      submissions: paginatedSubmissions,
      pagination: {
        currentPage: page,
        totalPages: totalPages,
        totalCount: totalCount,
        limit: limit,
        hasNext: page < totalPages,
        hasPrev: page > 1
      },
      filters: {
        status,
        search,
        dateFrom,
        dateTo
      }
    });
  } catch (err) {
    console.error('Error fetching submissions:', err);
    res.status(500).json({ message: 'Server error' });
  }
});



// Delete a submission
router.delete('/delete', ensureAuth, checkRole(['admin', 'editor']), async (req, res) => {
  try {
    const { submissionId } = req.body;
    console.log('Delete request for submission:', submissionId);

    if (!submissionId) {
      return res.status(400).json({ message: 'Submission ID is required' });
    }

    // Check if submission exists
    const submissionRef = db.collection('submissions').doc(submissionId);
    const submissionDoc = await submissionRef.get();

    if (!submissionDoc.exists) {
      return res.status(404).json({ message: 'Submission not found' });
    }

    // Delete the submission
    await submissionRef.delete();
    console.log('Submission deleted successfully:', submissionId);

    res.json({
      success: true,
      message: 'Submission deleted successfully.'
    });
  } catch (err) {
    console.error('Error deleting submission:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// Input validation helper functions
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function validatePhone(phone) {
    if (!phone) return true; // Phone is optional
    // Allow only numbers, spaces, and + sign, minimum 9 digits
    const phoneRegex = /^[\+]?[0-9\s]{9,15}$/;
    return phoneRegex.test(phone);
}

function sanitizeString(str) {
    if (typeof str !== 'string') return '';
    return str.trim().substring(0, 255); // Limit length and trim
}

function validateNumber(num, min = 0, max = 10000000) {
    const parsed = Number(num);
    return !isNaN(parsed) && parsed >= min && parsed <= max;
}

function validateFormData(data) {
    const errors = [];

    // Required fields with specific error messages
    if (!data.name || data.name.trim().length < 2) {
        errors.push('Jméno a příjmení: Musí obsahovat alespoň 2 znaky');
    }
    if (!data.email || !validateEmail(data.email)) {
        errors.push('Email: Zadejte platnou emailovou adresu (např. <EMAIL>)');
    }
    if (!data.address || data.address.trim().length < 5) {
        errors.push('Adresa: Musí obsahovat alespoň 5 znaků');
    }
    if (data.phone && !validatePhone(data.phone)) {
        errors.push('Telefon: Zadejte platné telefonní číslo (např. +420 123 456 789)');
    }

    // Validate date of birth if provided
    if (data.dateOfBirth && data.dateOfBirth.trim()) {
        // Try to parse the date - accept various formats
        let birthDate;
        const dateString = data.dateOfBirth.trim();

        // Try different date formats
        if (dateString.includes('.')) {
            // DD.MM.YYYY format
            const [day, month, year] = dateString.split('.').map(Number);
            birthDate = new Date(year, month - 1, day);
        } else if (dateString.includes('-')) {
            // YYYY-MM-DD format (HTML date input)
            birthDate = new Date(dateString);
        } else {
            // Try direct parsing
            birthDate = new Date(dateString);
        }

        // Check if the date is valid and before today
        const today = new Date();
        today.setHours(0, 0, 0, 0); // Start of today

        if (isNaN(birthDate.getTime())) {
            errors.push('Datum narození: Zadejte platné datum');
        } else if (birthDate >= today) {
            errors.push('Datum narození: Datum musí být před dnešním dnem');
        }
    }

    // Validate realization address if provided
    if (data.realizationAddress && data.realizationAddress.trim() && data.realizationAddress.trim().length < 5) {
        errors.push('Adresa realizace: Musí obsahovat alespoň 5 znaků');
    }

    // Validate numeric fields with user-friendly names
    const numericFieldsMap = {
        'facadeAreaSubsidized': 'Zateplení fasády - dotovaná plocha',
        'facadeAreaNonSubsidized': 'Zateplení fasády - nedotovaná plocha',
        'totalRevealLength': 'Zateplení fasády - délka ostění',
        'roofAreaSubsidized': 'Zateplení střechy - dotovaná plocha',
        'roofAreaNonSubsidized': 'Zateplení střechy - nedotovaná plocha',
        'atticAreaSubsidized': 'Zateplení podkroví - dotovaná plocha',
        'atticAreaNonSubsidized': 'Zateplení podkroví - nedotovaná plocha',
        'wallAreaSubsidized': 'Zateplení stěn - dotovaná plocha',
        'wallAreaNonSubsidized': 'Zateplení stěn - nedotovaná plocha',
        'floorAreaSubsidized': 'Zateplení podlahy - dotovaná plocha',
        'floorAreaNonSubsidized': 'Zateplení podlahy - nedotovaná plocha',
        'windowsAreaSubsidized': 'Výměna oken - dotovaná plocha',
        'windowsAreaNonSubsidized': 'Výměna oken - nedotovaná plocha',
        'shadingAreaSubsidized': 'Venkovní stínění - dotovaná plocha',
        'shadingAreaNonSubsidized': 'Venkovní stínění - nedotovaná plocha',
        'totalCost': 'Celková cena',
        'totalCostWithVAT': 'Celková cena s DPH',
        'totalSubsidy': 'Celková dotace',
        'finalCost': 'Konečná cena'
    };

    for (const [field, friendlyName] of Object.entries(numericFieldsMap)) {
        if (data[field] !== undefined && !validateNumber(data[field])) {
            errors.push(`${friendlyName}: Zadejte platné číslo (0-10,000,000)`);
        }
    }

    return errors;
}

// Form submission route
router.post('/submit-form', ensureAuth, async (req, res) => {
    try {
        console.log('=== FORM SUBMISSION RECEIVED ===');
        console.log('User:', req.user.displayName);
        console.log('Request body keys:', Object.keys(req.body));
        console.log('Request body sample:', {
            name: req.body.name,
            email: req.body.email,
            address: req.body.address,
            totalCost: req.body.totalCost
        });

        // Validate and sanitize input data
        const validationErrors = validateFormData(req.body);
        console.log('Validation errors:', validationErrors);

        if (validationErrors.length > 0) {
            console.log('Form validation failed:', validationErrors);

            // Create a user-friendly error message
            const errorMessage = validationErrors.length === 1
                ? `Chyba ve formuláři: ${validationErrors[0]}`
                : `Chyby ve formuláři:\n• ${validationErrors.join('\n• ')}`;

            return res.status(400).json({
                message: errorMessage,
                errors: validationErrors,
                count: validationErrors.length
            });
        }

        const {
            name, email, address, phone, dateOfBirth, realizationAddress,
            facadeAreaSubsidized, facadeAreaNonSubsidized, totalRevealLength,
            materialPolystyrene, materialMineralWool,
            roofAreaSubsidized, roofAreaNonSubsidized, additionalRoofWork,
            atticAreaSubsidized, atticAreaNonSubsidized, atticFloorRemoval, osbBoardArea, inspectionWalkwayLength,
            wallAreaSubsidized, wallAreaNonSubsidized, wallSdkCladding, wallPlastering,
            floorAreaSubsidized, floorAreaNonSubsidized,
            windowsAreaSubsidized, windowsAreaNonSubsidized,
            shadingAreaSubsidized, shadingAreaNonSubsidized, totalWindowWidth,
            heatPump8kW, heatPump12kW, heatPump16kW, heatPump22kW, heatPumpHeatingOnly, heatPumpHeatingWater,
            roofTypeSloped, roofTypeFlat, newBoilerYes, newBoilerNo,
            heatRecoveryYes, heatRecoveryNo, rainwaterTankSize,
            fveBattery5_4, fveBattery7_2, fveBattery9_9, fveBattery14_4, wallboxCount, gridConnectionYes,
            regionalBonus, childrenFullCare, childrenPartialCare, combinationBonusInsulationFVE, combinationBonusInsulationHeatSource,
            facadeCost, roofCost, atticCost, wallCost, floorCost, windowsCost, shadingCost, heatPumpCost, photovoltaicCost, fveBatteryCost, heatRecoveryCost, rainwaterCost,
            facadeSubsidy, roofSubsidy, atticSubsidy, wallSubsidy, floorSubsidy, windowsSubsidy, shadingSubsidy, heatPumpSubsidy, photovoltaicSubsidy, fveBatterySubsidy, heatRecoverySubsidy, rainwaterSubsidy,
            regionalBonusAmount, familyBonusAmount, combinationBonusAmount, basicSupportAmount, totalBonuses,
            totalCost, totalCostWithVAT, totalSubsidy, finalCost
        } = req.body;

    const userId = req.user.id;

    // Check user role - only admins and editors bypass the rate limit
    const userRole = req.user.role || 'user';
    const isPrivilegedUser = ['admin', 'editor'].includes(userRole);

    console.log(`User ${userId} has role: ${userRole}, privileged: ${isPrivilegedUser}`);

    // Only apply rate limiting for regular users and guests
    if (!isPrivilegedUser) {
        try {
            const userRef = db.collection('users').doc(userId);
            const userDoc = await userRef.get();

            if (userDoc.exists && userDoc.data().lastSubmission) {
                const lastSubmission = userDoc.data().lastSubmission;
                const now = admin.firestore.Timestamp.now();

                // Calculate time difference in seconds
                const timeDiff = now.seconds - lastSubmission.seconds;

                // If less than 60 seconds (1 minute) have passed
                if (timeDiff < 60) {
                    const timeRemaining = 60 - timeDiff;
                    console.log(`Rate limit applied for user ${userId}, must wait ${timeRemaining} seconds`);
                    return res.status(429).json({
                        message: `Prosím počkejte ${timeRemaining} sekund před odesláním dalšího formuláře.`,
                        timeRemaining: timeRemaining
                    });
                }
            }
        } catch (error) {
            console.error('Error checking submission rate limit:', error);
            // Continue with submission if there's an error checking the rate limit
        }
    } else {
        console.log(`Rate limit bypassed for privileged user ${userId} with role ${userRole}`);
    }

    console.log(`Received insulation calculator submission: Name - ${name}, Email - ${email}`);

    try {
        // Save submission to Firestore
        const submissionRef = await db.collection('submissions').add({
            userId: req.user.id,
            userDisplayName: req.user.displayName,
            userEmail: req.user.email,
            userProfileImage: req.user.profileImage,
            userRole: req.user.role || 'user', // Store user role for contract template selection

            // Personal information
            name,
            email,
            address,
            phone,
            dateOfBirth,
            realizationAddress,

            // Facade insulation data
            facadeAreaSubsidized: Number(facadeAreaSubsidized) || 0,
            facadeAreaNonSubsidized: Number(facadeAreaNonSubsidized) || 0,
            totalRevealLength: Number(totalRevealLength) || 0,
            materialPolystyrene: Boolean(materialPolystyrene),
            materialMineralWool: Boolean(materialMineralWool),

            // Roof insulation data
            roofAreaSubsidized: Number(roofAreaSubsidized) || 0,
            roofAreaNonSubsidized: Number(roofAreaNonSubsidized) || 0,
            additionalRoofWork: Boolean(additionalRoofWork),

            // Attic ceiling insulation data
            atticAreaSubsidized: Number(atticAreaSubsidized) || 0,
            atticAreaNonSubsidized: Number(atticAreaNonSubsidized) || 0,
            atticFloorRemoval: Boolean(atticFloorRemoval),
            osbBoardArea: Number(osbBoardArea) || 0,
            inspectionWalkwayLength: Number(inspectionWalkwayLength) || 0,

            // Wall insulation data
            wallAreaSubsidized: Number(wallAreaSubsidized) || 0,
            wallAreaNonSubsidized: Number(wallAreaNonSubsidized) || 0,
            wallSdkCladding: Boolean(wallSdkCladding),
            wallPlastering: Boolean(wallPlastering),

            // Floor insulation data
            floorAreaSubsidized: Number(floorAreaSubsidized) || 0,
            floorAreaNonSubsidized: Number(floorAreaNonSubsidized) || 0,

            // Windows and doors data
            windowsAreaSubsidized: Number(windowsAreaSubsidized) || 0,
            windowsAreaNonSubsidized: Number(windowsAreaNonSubsidized) || 0,

            // Shading technology data
            shadingAreaSubsidized: Number(shadingAreaSubsidized) || 0,
            shadingAreaNonSubsidized: Number(shadingAreaNonSubsidized) || 0,
            totalWindowWidth: Number(totalWindowWidth) || 0,

            // Heat pump data
            heatPump8kW: Boolean(heatPump8kW),
            heatPump12kW: Boolean(heatPump12kW),
            heatPump16kW: Boolean(heatPump16kW),
            heatPump22kW: Boolean(heatPump22kW),
            heatPumpHeatingOnly: Boolean(heatPumpHeatingOnly),
            heatPumpHeatingWater: Boolean(heatPumpHeatingWater),

            // Photovoltaic water heating data
            roofTypeSloped: Boolean(roofTypeSloped),
            roofTypeFlat: Boolean(roofTypeFlat),
            newBoilerYes: Boolean(newBoilerYes),
            newBoilerNo: Boolean(newBoilerNo),

            // Heat recovery data
            heatRecoveryYes: Boolean(heatRecoveryYes),
            heatRecoveryNo: Boolean(heatRecoveryNo),

            // Rainwater data
            rainwaterTankSize: Number(rainwaterTankSize) || 0,

            // FVE battery system data
            fveBattery5_4: Boolean(fveBattery5_4),
            fveBattery7_2: Boolean(fveBattery7_2),
            fveBattery9_9: Boolean(fveBattery9_9),
            fveBattery14_4: Boolean(fveBattery14_4),
            wallboxCount: Number(wallboxCount) || 0,
            gridConnectionYes: Boolean(gridConnectionYes),

            // Bonus data
            regionalBonus: Boolean(regionalBonus),
            childrenFullCare: Number(childrenFullCare) || 0,
            childrenPartialCare: Number(childrenPartialCare) || 0,
            combinationBonusInsulationFVE: Boolean(combinationBonusInsulationFVE),
            combinationBonusInsulationHeatSource: Boolean(combinationBonusInsulationHeatSource),

            // Cost calculations
            facadeCost: Number(facadeCost) || 0,
            roofCost: Number(roofCost) || 0,
            atticCost: Number(atticCost) || 0,
            wallCost: Number(wallCost) || 0,
            floorCost: Number(floorCost) || 0,
            windowsCost: Number(windowsCost) || 0,
            shadingCost: Number(shadingCost) || 0,
            heatPumpCost: Number(heatPumpCost) || 0,
            photovoltaicCost: Number(photovoltaicCost) || 0,
            fveBatteryCost: Number(fveBatteryCost) || 0,
            heatRecoveryCost: Number(heatRecoveryCost) || 0,
            rainwaterCost: Number(rainwaterCost) || 0,
            facadeSubsidy: Number(facadeSubsidy) || 0,
            roofSubsidy: Number(roofSubsidy) || 0,
            atticSubsidy: Number(atticSubsidy) || 0,
            wallSubsidy: Number(wallSubsidy) || 0,
            floorSubsidy: Number(floorSubsidy) || 0,
            windowsSubsidy: Number(windowsSubsidy) || 0,
            shadingSubsidy: Number(shadingSubsidy) || 0,
            heatPumpSubsidy: Number(heatPumpSubsidy) || 0,
            photovoltaicSubsidy: Number(photovoltaicSubsidy) || 0,
            fveBatterySubsidy: Number(fveBatterySubsidy) || 0,
            heatRecoverySubsidy: Number(heatRecoverySubsidy) || 0,
            rainwaterSubsidy: Number(rainwaterSubsidy) || 0,
            // Bonus calculations
            regionalBonusAmount: Number(regionalBonusAmount) || 0,
            familyBonusAmount: Number(familyBonusAmount) || 0,
            combinationBonusAmount: Number(combinationBonusAmount) || 0,
            basicSupportAmount: Number(basicSupportAmount) || 0,
            totalBonuses: Number(totalBonuses) || 0,

            // Summary calculations
            totalCost: Number(totalCost) || 0,
            totalCostWithVAT: Number(totalCostWithVAT) || 0,
            totalSubsidy: Number(totalSubsidy) || 0,
            finalCost: Number(finalCost) || 0,

            // Metadata
            type: 'insulation-calculator',
            status: 'approved', // Automatically approved since email is sent automatically
            createdAt: admin.firestore.FieldValue.serverTimestamp()
        });

        // Update the user's last submission timestamp (only for non-privileged users)
        if (!isPrivilegedUser) {
            await db.collection('users').doc(req.user.id).update({
                lastSubmission: admin.firestore.FieldValue.serverTimestamp()
            });
            console.log(`Updated last submission timestamp for user ${req.user.id}`);
        }

        console.log(`Insulation calculator submission saved with ID: ${submissionRef.id}`);

        // Automatically send email with PDFs after successful submission
        try {
            console.log('Attempting to automatically send email with PDFs (v2)...');

            // Create the submission data object for email generation
            const submissionForEmail = {
                id: submissionRef.id,
                userId: req.user.id,
                userDisplayName: req.user.displayName,
                userEmail: req.user.email,
                userProfileImage: req.user.profileImage,
                userRole: req.user.role || 'user', // Include user role for contract template selection
                // Personal information
                name,
                email, // Use form email for contract content
                address,
                phone,
                dateOfBirth,
                realizationAddress,
                // Facade insulation data
                facadeAreaSubsidized: Number(facadeAreaSubsidized) || 0,
                facadeAreaNonSubsidized: Number(facadeAreaNonSubsidized) || 0,
                totalRevealLength: Number(totalRevealLength) || 0,
                materialPolystyrene: Boolean(materialPolystyrene),
                materialMineralWool: Boolean(materialMineralWool),
                // Roof insulation data
                roofAreaSubsidized: Number(roofAreaSubsidized) || 0,
                roofAreaNonSubsidized: Number(roofAreaNonSubsidized) || 0,
                additionalRoofWork: Boolean(additionalRoofWork),
                // Attic ceiling insulation data
                atticAreaSubsidized: Number(atticAreaSubsidized) || 0,
                atticAreaNonSubsidized: Number(atticAreaNonSubsidized) || 0,
                atticFloorRemoval: Boolean(atticFloorRemoval),
                osbBoardArea: Number(osbBoardArea) || 0,
                inspectionWalkwayLength: Number(inspectionWalkwayLength) || 0,
                // Wall insulation data
                wallAreaSubsidized: Number(wallAreaSubsidized) || 0,
                wallAreaNonSubsidized: Number(wallAreaNonSubsidized) || 0,
                wallSdkCladding: Boolean(wallSdkCladding),
                wallPlastering: Boolean(wallPlastering),
                // Floor insulation data
                floorAreaSubsidized: Number(floorAreaSubsidized) || 0,
                floorAreaNonSubsidized: Number(floorAreaNonSubsidized) || 0,
                // Windows and doors data
                windowsAreaSubsidized: Number(windowsAreaSubsidized) || 0,
                windowsAreaNonSubsidized: Number(windowsAreaNonSubsidized) || 0,
                // Shading technology data
                shadingAreaSubsidized: Number(shadingAreaSubsidized) || 0,
                shadingAreaNonSubsidized: Number(shadingAreaNonSubsidized) || 0,
                totalWindowWidth: Number(totalWindowWidth) || 0,
                // Heat pump data
                heatPump8kW: Boolean(heatPump8kW),
                heatPump12kW: Boolean(heatPump12kW),
                heatPump16kW: Boolean(heatPump16kW),
                heatPump22kW: Boolean(heatPump22kW),
                heatPumpHeatingOnly: Boolean(heatPumpHeatingOnly),
                heatPumpHeatingWater: Boolean(heatPumpHeatingWater),
                // Photovoltaic water heating data
                roofTypeSloped: Boolean(roofTypeSloped),
                roofTypeFlat: Boolean(roofTypeFlat),
                newBoilerYes: Boolean(newBoilerYes),
                newBoilerNo: Boolean(newBoilerNo),
                // Heat recovery data
                heatRecoveryYes: Boolean(heatRecoveryYes),
                heatRecoveryNo: Boolean(heatRecoveryNo),
                // Rainwater data
                rainwaterTankSize: Number(rainwaterTankSize) || 0,
                // FVE battery system data
                fveBattery5_4: Boolean(fveBattery5_4),
                fveBattery7_2: Boolean(fveBattery7_2),
                fveBattery9_9: Boolean(fveBattery9_9),
                fveBattery14_4: Boolean(fveBattery14_4),
                wallboxCount: Number(wallboxCount) || 0,
                gridConnectionYes: Boolean(gridConnectionYes),
                // Bonus data
                regionalBonus: Boolean(regionalBonus),
                childrenFullCare: Number(childrenFullCare) || 0,
                childrenPartialCare: Number(childrenPartialCare) || 0,
                combinationBonusInsulationFVE: Boolean(combinationBonusInsulationFVE),
                combinationBonusInsulationHeatSource: Boolean(combinationBonusInsulationHeatSource),
                // Cost calculations
                facadeCost: Number(facadeCost) || 0,
                roofCost: Number(roofCost) || 0,
                atticCost: Number(atticCost) || 0,
                wallCost: Number(wallCost) || 0,
                floorCost: Number(floorCost) || 0,
                windowsCost: Number(windowsCost) || 0,
                shadingCost: Number(shadingCost) || 0,
                heatPumpCost: Number(heatPumpCost) || 0,
                photovoltaicCost: Number(photovoltaicCost) || 0,
                fveBatteryCost: Number(fveBatteryCost) || 0,
                heatRecoveryCost: Number(heatRecoveryCost) || 0,
                rainwaterCost: Number(rainwaterCost) || 0,
                facadeSubsidy: Number(facadeSubsidy) || 0,
                roofSubsidy: Number(roofSubsidy) || 0,
                atticSubsidy: Number(atticSubsidy) || 0,
                wallSubsidy: Number(wallSubsidy) || 0,
                floorSubsidy: Number(floorSubsidy) || 0,
                windowsSubsidy: Number(windowsSubsidy) || 0,
                shadingSubsidy: Number(shadingSubsidy) || 0,
                heatPumpSubsidy: Number(heatPumpSubsidy) || 0,
                photovoltaicSubsidy: Number(photovoltaicSubsidy) || 0,
                fveBatterySubsidy: Number(fveBatterySubsidy) || 0,
                heatRecoverySubsidy: Number(heatRecoverySubsidy) || 0,
                rainwaterSubsidy: Number(rainwaterSubsidy) || 0,
                // Bonus calculations
                regionalBonusAmount: Number(regionalBonusAmount) || 0,
                familyBonusAmount: Number(familyBonusAmount) || 0,
                combinationBonusAmount: Number(combinationBonusAmount) || 0,
                basicSupportAmount: Number(basicSupportAmount) || 0,
                totalBonuses: Number(totalBonuses) || 0,
                // Summary calculations
                totalCost: Number(totalCost) || 0,
                totalCostWithVAT: Number(totalCostWithVAT) || 0,
                totalSubsidy: Number(totalSubsidy) || 0,
                finalCost: Number(finalCost) || 0,
                // Metadata
                type: 'insulation-calculator',
                status: 'approved'
            };

            // Import the email function from server.js
            const { generateAndSendPDF } = require('../server');

            // Send email with both PDFs automatically
            await generateAndSendPDF(submissionForEmail);

            console.log('Automatic email sent successfully');

            res.status(200).json({
                message: 'Formulář byl úspěšně odeslán! Email s PDF dokumenty byl automaticky odeslán na vaši adresu.',
                submissionId: submissionRef.id,
                emailSent: true
            });
        } catch (emailError) {
            console.error('Error sending automatic email:', emailError);
            console.error('Email error details:', {
                code: emailError.code,
                response: emailError.response,
                responseCode: emailError.responseCode,
                command: emailError.command
            });

            // Still return success for form submission, but note email issue
            res.status(200).json({
                message: 'Formulář byl úspěšně odeslán! Email se nepodařilo odeslat kvůli problému s konfigurací. Kontaktujte administrátora.',
                submissionId: submissionRef.id,
                emailSent: false,
                emailError: emailError.message
            });
        }
    } catch (error) {
        console.error('Error saving submission:', error);
        res.status(500).json({ message: 'Nepodařilo se uložit váš formulář. Zkuste to prosím později.' });
    }
    } catch (error) {
        console.error('Error in form submission route:', error);
        res.status(500).json({ message: 'Nepodařilo se zpracovat váš formulář. Zkuste to prosím později.' });
    }
});

// Generate document from Word template
router.post('/generate-document', ensureAuth, checkRole(['admin', 'editor']), async (req, res) => {
    try {
        console.log('=== Generate Document Route Called ===');
        console.log('Request body:', req.body);
        console.log('User:', req.user.id);

        const { submissionId, templateName } = req.body;

        if (!submissionId || !templateName) {
            console.error('Missing required parameters:', { submissionId, templateName });
            return res.status(400).json({
                message: 'Submission ID and template name are required'
            });
        }

        console.log('Getting submission from Firestore...');
        // Get submission data from Firestore
        const submissionRef = db.collection('submissions').doc(submissionId);
        const submissionDoc = await submissionRef.get();

        if (!submissionDoc.exists) {
            console.error('Submission not found:', submissionId);
            return res.status(404).json({ message: 'Submission not found' });
        }

        const submissionData = submissionDoc.data();
        console.log('Submission data retrieved successfully');

        // Prepare form data for document generation
        const formData = {
            // Customer information
            customerName: submissionData.name || '',
            customerAddress: submissionData.address || '',
            dateOfBirth: submissionData.dateOfBirth || '',
            email: submissionData.email || '',
            phone: submissionData.phone || '',
            propertyAddress: submissionData.propertyAddress || submissionData.address || '',
            realizationAddress: submissionData.realizationAddress || '',

            // Financial information
            totalCost: submissionData.totalCost || 0,
            totalCostWithVAT: submissionData.totalCostWithVAT || 0,
            totalSubsidy: submissionData.totalSubsidy || 0,
            finalCost: submissionData.finalCost || 0,

            // Form selections
            facadeSubsidizedArea: submissionData.facadeAreaSubsidized || 0,
            facadeNonSubsidizedArea: submissionData.facadeAreaNonSubsidized || 0,
            facadePolystyrene: submissionData.materialPolystyrene || false,
            facadeMineralWool: submissionData.materialMineralWool || false,

            // Heat pump selections
            heatPump8kW: submissionData.heatPump8kW || false,
            heatPump12kW: submissionData.heatPump12kW || false,
            heatPump16kW: submissionData.heatPump16kW || false,
            heatPump22kW: submissionData.heatPump22kW || false,
            heatPumpHeatingOnly: submissionData.heatPumpHeatingOnly || false,
            heatPumpHeatingWater: submissionData.heatPumpHeatingWater || false,

            // Photovoltaic selections
            roofTypeSloped: submissionData.roofTypeSloped || false,
            roofTypeFlat: submissionData.roofTypeFlat || false,
            newBoilerYes: submissionData.newBoilerYes || false,
            newBoilerNo: submissionData.newBoilerNo || false,

            // Wall additional work
            wallSdkCladding: submissionData.wallSdkCladding || false,
            wallPlastering: submissionData.wallPlastering || false
        };

        console.log('Preparing form data...');
        console.log('Form data prepared:', JSON.stringify(formData, null, 2));

        // Generate unique filename
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const outputName = `${templateName}_${submissionId}_${timestamp}`;

        console.log('Template name:', templateName);
        console.log('Output name:', outputName);

        console.log('Calling documentService.generateDocument...');
        console.log('DocumentService object:', typeof documentService);
        console.log('DocumentService methods:', Object.getOwnPropertyNames(documentService));

        // Generate the document
        const result = await documentService.generateDocument(templateName, outputName, formData);

        console.log('Document generation result:', result);

        if (result.success) {
            console.log('Document generated successfully, updating Firestore...');
            // Update submission with generated document info
            await submissionRef.update({
                generatedDocument: {
                    templateName,
                    pdfPath: result.pdfPath,
                    generatedAt: admin.firestore.FieldValue.serverTimestamp(),
                    generatedBy: req.user.id,
                    generatedByName: req.user.displayName,
                    pdfError: result.pdfError || null
                }
            });

            console.log('Firestore updated, sending response...');
            res.json({
                success: true,
                message: 'Document generated successfully',
                pdfPath: result.pdfPath,
                pdfError: result.pdfError || null
            });
        } else {
            console.error('Document generation failed - result.success is false');
            console.error('Error details:', result.error);
            console.error('Error type:', result.errorType);
            res.status(500).json({
                message: 'Failed to generate document',
                error: result.error,
                errorType: result.errorType
            });
        }

    } catch (error) {
        console.error('=== ERROR in generate-document route ===');
        console.error('Error type:', error.constructor.name);
        console.error('Error message:', error.message);
        console.error('Error stack:', error.stack);
        res.status(500).json({
            message: 'Server error while generating document',
            error: error.message,
            errorType: error.constructor.name
        });
    }
});

// Download generated document
router.get('/download/:submissionId/:type', ensureAuth, checkRole(['admin', 'editor']), async (req, res) => {
    try {
        const { submissionId, type } = req.params;

        if (!['pdf', 'docx'].includes(type)) {
            return res.status(400).json({ message: 'Invalid file type' });
        }

        // Get submission data
        const submissionRef = db.collection('submissions').doc(submissionId);
        const submissionDoc = await submissionRef.get();

        if (!submissionDoc.exists) {
            return res.status(404).json({ message: 'Submission not found' });
        }

        const submissionData = submissionDoc.data();

        if (!submissionData.generatedDocument) {
            return res.status(404).json({ message: 'No generated document found' });
        }

        const filePath = submissionData.generatedDocument.pdfPath;

        // Check if file exists
        const fs = require('fs');
        if (!fs.existsSync(filePath)) {
            return res.status(404).json({ message: 'Document file not found' });
        }

        // Set appropriate headers
        const filename = `submission_${submissionId}.pdf`;
        res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
        res.setHeader('Content-Type', 'application/pdf');

        // Send file
        res.sendFile(filePath);

    } catch (error) {
        console.error('Error downloading document:', error);
        res.status(500).json({ message: 'Server error while downloading document' });
    }
});

module.exports = router;
