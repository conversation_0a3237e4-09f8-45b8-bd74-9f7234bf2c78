{"name": "simple-form-app", "version": "1.0.0", "description": "A simple web app with a form and simulated email sending.", "main": "server.js", "scripts": {"start": "node server.js"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@getbrevo/brevo": "^3.0.1", "@pdf-lib/fontkit": "^1.1.1", "compression": "^1.8.0", "connect-firestore": "^0.1.9", "connect-mongo": "^5.1.0", "dotenv": "^16.5.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "firebase-admin": "^13.4.0", "fs-extra": "^11.3.0", "helmet": "^8.1.0", "multer": "^1.4.5-lts.2", "nodemailer": "^6.10.0", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "pdfkit": "^0.16.0"}}