const PDFDocument = require('pdfkit');
const fs = require('fs');
const path = require('path');
const stream = require('stream');
const { db } = require('./firebase-admin');

// Note: Using pre-compressed images from public/templates/compressed/ for smaller PDF file sizes

// Default pricing configuration (fallback)
const defaultPricing = {
    bonuses: {
        childFullCare: 50000,
        childPartialCare: 25000,
        combinationInsulationFVE: 50000,
        combinationInsulationHeatSource: 50000,
        basicSupport: 50000
    }
};

// Function to load pricing configuration
async function loadPricingConfiguration() {
    try {
        const pricingDoc = await db.collection('settings').doc('pricing').get();

        if (!pricingDoc.exists) {
            console.log('No pricing configuration found, using defaults');
            return defaultPricing;
        }

        const pricing = pricingDoc.data();
        console.log('Pricing configuration loaded for PDF generation');
        return pricing;
    } catch (error) {
        console.error('Error loading pricing configuration for PDF:', error);
        return defaultPricing;
    }
}

// Function to generate PDF using templates
async function generateTemplatePDF(submission) {
    console.log('Starting template PDF generation with size optimization...');

    // Load pricing configuration
    const pricingConfig = await loadPricingConfiguration();

    // Create a new PDF document with aggressive compression optimizations
    const doc = new PDFDocument({
        autoFirstPage: false, // We'll add pages manually
        size: 'A4',
        margin: 0, // No margins since we're using full-page templates
        bufferPages: true,
        // Aggressive PDF compression optimizations
        compress: true, // Enable PDF compression
        pdfVersion: '1.4', // Use older PDF version for better compression
        info: {
            Title: 'Kalkulace zateplení',
            Author: 'Kalkulace',
            Subject: 'Cenová nabídka zateplení',
            Creator: 'Kalkulace App',
            Producer: 'PDFKit'
        }
    });

    const pdfChunks = [];
    const pdfStream = doc.pipe(new stream.PassThrough());
    pdfStream.on('data', (chunk) => pdfChunks.push(chunk));

    console.log('Registering font...');
    try {
        // Register font for text overlay
        doc.registerFont('dejavu', 'fonts/DejaVuSans.ttf');
        console.log('Font registered successfully');
    } catch (err) {
        console.error('Error registering font:', err);
    }

    // Extract relevant data from submission
    const {
        name, email, address, phone,
        totalCost = 0,
        totalCostWithVAT = 0,
        totalSubsidy = 0,
        finalCost = 0,
        // Individual cost components
        facadeCost = 0,
        roofCost = 0,
        atticCost = 0,
        wallCost = 0,
        floorCost = 0,
        windowsCost = 0,
        shadingCost = 0,
        heatPumpCost = 0,
        photovoltaicCost = 0,
        // Individual subsidy components
        facadeSubsidy = 0,
        roofSubsidy = 0,
        atticSubsidy = 0,
        wallSubsidy = 0,
        floorSubsidy = 0,
        windowsSubsidy = 0,
        shadingSubsidy = 0,
        heatPumpSubsidy = 0,
        photovoltaicSubsidy = 0,
        fveBatteryCost = 0,
        fveBatterySubsidy = 0,
        // Bonus components
        regionalBonusAmount = 0,
        familyBonusAmount = 0,
        combinationBonusAmount = 0,
        basicSupportAmount = 0
    } = submission;

    // Debug: Log the bonus amounts to see what's being passed
    console.log('PDF Generation - Bonus amounts:');
    console.log('- regionalBonusAmount:', regionalBonusAmount);
    console.log('- familyBonusAmount:', familyBonusAmount);
    console.log('- combinationBonusAmount:', combinationBonusAmount);
    console.log('- basicSupportAmount:', basicSupportAmount);
    console.log('- totalSubsidy:', totalSubsidy);

    // Debug: Log combination bonus flags
    console.log('PDF Generation - Combination bonus flags:');
    console.log('- combinationBonusInsulationHeatSource:', submission.combinationBonusInsulationHeatSource);
    console.log('- combinationBonusInsulationFVE:', submission.combinationBonusInsulationFVE);
    console.log('- heatPump8kW:', submission.heatPump8kW);
    console.log('- heatPump12kW:', submission.heatPump12kW);
    console.log('- heatPump16kW:', submission.heatPump16kW);
    console.log('- heatPump22kW:', submission.heatPump22kW);
    console.log('- fveBattery5_4:', submission.fveBattery5_4);
    console.log('- fveBattery7_2:', submission.fveBattery7_2);
    console.log('- fveBattery9_9:', submission.fveBattery9_9);
    console.log('- fveBattery14_4:', submission.fveBattery14_4);
    console.log('- roofTypeSloped:', submission.roofTypeSloped);
    console.log('- roofTypeFlat:', submission.roofTypeFlat);
    console.log('- photovoltaicCost:', photovoltaicCost);
    console.log('- fveBatteryCost:', fveBatteryCost);

    // Add first page (cover page)
    try {
        console.log('Adding first page...');
        doc.addPage();

        // Get the full path to the compressed template image
        const imagePath1 = path.join(__dirname, 'public', 'templates', 'compressed', 'page1.jpg');
        console.log('Image path 1:', imagePath1);
        console.log('Image exists:', fs.existsSync(imagePath1));

        // Add the compressed template image as background
        doc.image(imagePath1, 0, 0, {
            width: doc.page.width,
            height: doc.page.height
        });
        console.log('First page image added successfully');

        // Add client information aligned with template fields
        doc.font('dejavu').fontSize(12).fillColor('#000000');

        // Calculate positions based on template image (1645x2339px scaled to A4)
        // Template fields are positioned around 38% down the page
        // A4 page height is 842 points, so we need to position around y=530-570
        const baseY = 515; // Starting Y position for client info section (moved up)
        const lineHeight = 20; // Space between lines
        const labelX = 120; // X position after the labels (moved left)

        // Position client information to align with template labels
        doc.text(`${name || ''}`, labelX, baseY);                    // After "Klient:"
        doc.text(`${address || ''}`, labelX, baseY + lineHeight);    // After "Adresa:"
        doc.text(`${phone || ''}`, labelX, baseY + lineHeight * 2);  // After "Telefon:"
        doc.text(`${email || ''}`, labelX, baseY + lineHeight * 3);  // After "Email:"
        console.log('Client information added to first page');
    } catch (err) {
        console.error('Error adding first page:', err);
    }

    // Add second page (items list)
    try {
        console.log('Adding second page...');
        doc.addPage();

        // Get the full path to the compressed template image
        const imagePath2 = path.join(__dirname, 'public', 'templates', 'compressed', 'page2.jpg');
        console.log('Image path 2:', imagePath2);
        console.log('Image exists:', fs.existsSync(imagePath2));

        // Add the compressed template image as background
        doc.image(imagePath2, 0, 0, {
            width: doc.page.width,
            height: doc.page.height
        });
        console.log('Second page image added successfully');

        // Add detailed breakdown table
        doc.font('dejavu').fontSize(10).fillColor('#000000');

        // Format numbers with thousand separators
        const formatNumber = (num) => Math.round(num).toLocaleString('cs-CZ');

        // Table positioning based on template (1645x2339px scaled to A4)
        const tableStartY = 200; // Starting Y position for table rows
        const rowHeight = 25; // Height of each table row
        const vatRate = '12 %'; // VAT rate

        // Column positions (X coordinates)
        const itemCol = 85; // Item description column
        const priceCol = 445; // Price without VAT column
        const vatCol = 535; // VAT rate column
        const totalCol = 580; // Total with VAT column

        let currentY = tableStartY;

        // Create array of items to display (only non-zero costs)
        const items = [];

        if (facadeCost > 0) {
            const totalFacadeArea = (submission.facadeAreaSubsidized || 0) + (submission.facadeAreaNonSubsidized || 0);
            items.push({
                name: `CN_001 Zateplení fasády systém maxit ${totalFacadeArea} m2 vč. lešení, D+M svodů`,
                cost: facadeCost,
                costWithVAT: facadeCost * 1.12
            });

            // CN_002: Subsidy for facade insulation
            if (facadeSubsidy > 0) {
                items.push({
                    name: `CN_002 Dotace zateplení fasády ${formatNumber(facadeSubsidy)} Kč`,
                    cost: -facadeSubsidy,
                    costWithVAT: -facadeSubsidy,
                    isSubsidy: true
                });
            }
        }

        if (atticCost > 0) {
            const totalAtticArea = (submission.atticAreaSubsidized || 0) + (submission.atticAreaNonSubsidized || 0);
            items.push({
                name: `CN_003 Zateplení stropu foukanou izolací ${totalAtticArea} m2`,
                cost: atticCost,
                costWithVAT: atticCost * 1.12
            });

            // CN_004: Subsidy for attic insulation
            if (atticSubsidy > 0) {
                items.push({
                    name: `CN_004 Dotace zateplení stropu ${formatNumber(atticSubsidy)} Kč`,
                    cost: -atticSubsidy,
                    costWithVAT: -atticSubsidy,
                    isSubsidy: true
                });
            }
        }

        if (wallCost > 0) {
            const totalWallArea = (submission.wallAreaSubsidized || 0) + (submission.wallAreaNonSubsidized || 0);
            items.push({
                name: `CN_027 Zateplení stěny k nevytápěné místnosti ${totalWallArea} m2`,
                cost: wallCost,
                costWithVAT: wallCost * 1.12
            });

            // CN_028: Subsidy for wall insulation
            const wallSubsidy = (submission.wallAreaSubsidized || 0) * 500; // Default subsidy rate
            if (wallSubsidy > 0) {
                items.push({
                    name: `CN_028 Dotace zateplení stěny k nevytápěné místnosti ${formatNumber(wallSubsidy)} Kč`,
                    cost: -wallSubsidy,
                    costWithVAT: -wallSubsidy,
                    isSubsidy: true
                });
            }
        }

        if (floorCost > 0) {
            const totalFloorArea = (submission.floorAreaSubsidized || 0) + (submission.floorAreaNonSubsidized || 0);
            items.push({
                name: `CN_007 Zateplení podlahy ${totalFloorArea} m2`,
                cost: floorCost,
                costWithVAT: floorCost * 1.12
            });

            // CN_008: Subsidy for floor insulation
            if (floorSubsidy > 0) {
                items.push({
                    name: `CN_008 Dotace zateplení podlahy ${formatNumber(floorSubsidy)} Kč`,
                    cost: -floorSubsidy,
                    costWithVAT: -floorSubsidy,
                    isSubsidy: true
                });
            }
        }

        if (windowsCost > 0) {
            items.push({
                name: 'CN_005 Výměna oken a dveří',
                cost: windowsCost,
                costWithVAT: windowsCost * 1.12
            });

            // CN_006: Subsidy for windows
            if (windowsSubsidy > 0) {
                items.push({
                    name: `CN_006 Dotace výplně otvorů ${formatNumber(windowsSubsidy)} Kč`,
                    cost: -windowsSubsidy,
                    costWithVAT: -windowsSubsidy,
                    isSubsidy: true
                });
            }
        }

        // Add roof insulation if present
        const roofCost = submission.roofCost || 0;
        if (roofCost > 0) {
            const totalRoofArea = (submission.roofAreaSubsidized || 0) + (submission.roofAreaNonSubsidized || 0);
            items.push({
                name: `CN_025 Zateplení střechy ${totalRoofArea} m2`,
                cost: roofCost,
                costWithVAT: roofCost * 1.12
            });

            // CN_026: Subsidy for roof insulation
            const roofSubsidy = (submission.roofAreaSubsidized || 0) * 1300; // Default subsidy rate
            if (roofSubsidy > 0) {
                items.push({
                    name: `CN_026 Dotace zateplení střechy ${formatNumber(roofSubsidy)} Kč`,
                    cost: -roofSubsidy,
                    costWithVAT: -roofSubsidy,
                    isSubsidy: true
                });
            }
        }

        if (shadingCost > 0) {
            items.push({
                name: 'CN_009 Stínící technika',
                cost: shadingCost,
                costWithVAT: shadingCost * 1.12
            });

            // CN_010: Subsidy for shading
            if (shadingSubsidy > 0) {
                items.push({
                    name: `CN_010 Dotace stínící technika ${formatNumber(shadingSubsidy)} Kč`,
                    cost: -shadingSubsidy,
                    costWithVAT: -shadingSubsidy,
                    isSubsidy: true
                });
            }
        }

        if (heatPumpCost > 0) {
            const heatPumpPower = getHeatPumpPower(submission);
            items.push({
                name: `CN_015 Tepelné čerpadlo ${heatPumpPower} kW`,
                cost: heatPumpCost,
                costWithVAT: heatPumpCost * 1.12
            });

            // CN_016: Subsidy for heat pump
            const heatPumpSubsidy = calculateHeatPumpSubsidy(submission);
            if (heatPumpSubsidy > 0) {
                items.push({
                    name: `CN_016 Dotace tepelné čerpadlo ${formatNumber(heatPumpSubsidy)} Kč`,
                    cost: -heatPumpSubsidy,
                    costWithVAT: -heatPumpSubsidy,
                    isSubsidy: true
                });
            }
        }

        if (photovoltaicCost > 0) {
            items.push({
                name: 'CN_013 Fotovoltaický ohřev vody',
                cost: photovoltaicCost,
                costWithVAT: photovoltaicCost * 1.12
            });

            // CN_014: Subsidy for photovoltaic water heating
            if (photovoltaicSubsidy > 0) {
                items.push({
                    name: `CN_014 Dotace fotovoltaický ohřev vody ${formatNumber(photovoltaicSubsidy)} Kč`,
                    cost: -photovoltaicSubsidy,
                    costWithVAT: -photovoltaicSubsidy,
                    isSubsidy: true
                });
            }
        }

        // Helper function for heat pump power
        function getHeatPumpPower(data) {
            if (data.heatPump8kW) return 8;
            if (data.heatPump12kW) return 12;
            if (data.heatPump16kW) return 16;
            if (data.heatPump22kW) return 22;
            return 0;
        }

        // Helper function for heat pump subsidy calculation
        function calculateHeatPumpSubsidy(data) {
            // Use configurable pricing based on heat pump usage type, not power rating
            if (data.heatPumpHeatingOnly) {
                return pricingConfig.heatPump?.subsidyHeatingOnly || 75000;
            } else if (data.heatPumpHeatingWater) {
                return pricingConfig.heatPump?.subsidyHeatingWater || 90000;
            }
            return 0;
        }



        // Add FVE battery system if present - calculate cost directly
        const calculatedFveCost = calculateFveCost(submission);
        const calculatedFveSubsidy = calculateFveSubsidy(submission);

        if (calculatedFveCost > 0) {
            const fveDescription = getFveDescription(submission);
            items.push({
                name: `CN_011 ${fveDescription}`,
                cost: calculatedFveCost,
                costWithVAT: calculatedFveCost * 1.12
            });

            // CN_012: Subsidy for FVE battery system
            if (calculatedFveSubsidy > 0) {
                items.push({
                    name: `CN_012 Dotace fotovoltaika ${formatNumber(calculatedFveSubsidy)} Kč`,
                    cost: -calculatedFveSubsidy,
                    costWithVAT: -calculatedFveSubsidy,
                    isSubsidy: true
                });
            }
        }

        // Add heat recovery if present
        if (submission.heatRecoveryYes) {
            items.push({
                name: 'CN_017 Rekuperace',
                cost: submission.heatRecoveryCost || 0,
                costWithVAT: (submission.heatRecoveryCost || 0) * 1.12
            });

            // CN_018: Subsidy for heat recovery
            const heatRecoverySubsidy = calculateHeatRecoverySubsidy(submission);
            if (heatRecoverySubsidy > 0) {
                items.push({
                    name: `CN_018 Dotace rekuperace ${formatNumber(heatRecoverySubsidy)} Kč`,
                    cost: -heatRecoverySubsidy,
                    costWithVAT: -heatRecoverySubsidy,
                    isSubsidy: true
                });
            }
        }

        // Add rainwater system if present
        const rainwaterTankSize = submission.rainwaterTankSize || 0;
        if (rainwaterTankSize > 0) {
            const rainwaterCost = calculateRainwaterCost(submission);
            items.push({
                name: `CN_019 Dešťovka ${rainwaterTankSize} m3`,
                cost: rainwaterCost,
                costWithVAT: rainwaterCost * 1.12
            });

            // CN_020: Subsidy for rainwater system
            const rainwaterSubsidy = calculateRainwaterSubsidy(submission);
            if (rainwaterSubsidy > 0) {
                items.push({
                    name: `CN_020 Dotace dešťovka ${formatNumber(rainwaterSubsidy)} Kč`,
                    cost: -rainwaterSubsidy,
                    costWithVAT: -rainwaterSubsidy,
                    isSubsidy: true
                });
            }
        }

        // CN_021: Family bonus for children
        const familyBonus = calculateFamilyBonus(submission);
        if (familyBonus > 0) {
            items.push({
                name: `CN_021 Rodinný bonus za každé nezletilé dítě ${formatNumber(familyBonus)} Kč`,
                cost: -familyBonus, // Family bonus is a subsidy (negative cost)
                costWithVAT: -familyBonus, // No VAT on subsidies
                isSubsidy: true
            });
        }

        // CN_022: Combination bonus for insulation + heat source (works independently when checked)
        if (submission.combinationBonusInsulationHeatSource) {
            const bonusAmount = pricingConfig.bonuses?.combinationInsulationHeatSource || 50000;
            items.push({
                name: `CN_022 KB zateplení + zdroj tepla ${formatNumber(bonusAmount)} Kč`,
                cost: -bonusAmount, // Combination bonus is a subsidy (negative cost)
                costWithVAT: -bonusAmount, // No VAT on subsidies
                isSubsidy: true
            });
        }

        // CN_023: Combination bonus for insulation + FVE (works independently when checked)
        if (submission.combinationBonusInsulationFVE) {
            const bonusAmount = pricingConfig.bonuses?.combinationInsulationFVE || 50000;
            items.push({
                name: `CN_023 KB zateplení + FVE ${formatNumber(bonusAmount)} Kč`,
                cost: -bonusAmount, // Combination bonus is a subsidy (negative cost)
                costWithVAT: -bonusAmount, // No VAT on subsidies
                isSubsidy: true
            });
        }

        // CN_024: Always include this item (basic support)
        const basicSupportAmount = pricingConfig.bonuses?.basicSupport || 50000;
        items.push({
            name: `CN_024 Základní podpora ${formatNumber(basicSupportAmount)} Kč`,
            cost: -basicSupportAmount, // Basic support is a subsidy (negative cost)
            costWithVAT: -basicSupportAmount, // No VAT on subsidies
            isSubsidy: true
        });

        // Add regional bonus if present (5% bonus)
        if (regionalBonusAmount > 0) {
            items.push({
                name: `Bonus pro vybrané obce a regiony (5%) ${formatNumber(regionalBonusAmount)} Kč`,
                cost: -regionalBonusAmount, // Regional bonus is a subsidy (negative cost)
                costWithVAT: -regionalBonusAmount, // No VAT on subsidies
                isSubsidy: true
            });
        }

        // Helper function for heat recovery subsidy calculation
        function calculateHeatRecoverySubsidy(data) {
            // Use configurable heat recovery subsidy
            return data.heatRecoveryYes ? (pricingConfig.heatRecovery?.subsidy || 90000) : 0;
        }





        // Helper function for FVE cost calculation
        function calculateFveCost(data) {
            let cost = 0;

            if (data.fveBattery5_4) {
                cost = pricingConfig.fveBattery?.system5_4kWp || 260000;
            } else if (data.fveBattery7_2) {
                cost = pricingConfig.fveBattery?.system7_2kWp || 300000;
            } else if (data.fveBattery9_9) {
                cost = pricingConfig.fveBattery?.system9_9kWp || 320000;
            } else if (data.fveBattery14_4) {
                cost = pricingConfig.fveBattery?.system14_4kWp || 410000;
            }

            // Add wallbox costs
            if (data.wallboxCount && data.wallboxCount > 0) {
                const wallboxCost = pricingConfig.fveBattery?.wallboxCost || 15000;
                cost += data.wallboxCount * wallboxCost;
            }

            return cost;
        }

        // Helper function for FVE subsidy calculation
        function calculateFveSubsidy(data) {
            // Only calculate subsidy if an FVE system is selected
            if (data.fveBattery5_4 || data.fveBattery7_2 || data.fveBattery9_9 || data.fveBattery14_4) {
                let subsidy = pricingConfig.fveBattery?.baseSubsidy || 100000;

                // Add grid connection subsidy if selected
                if (data.gridConnectionYes) {
                    subsidy += pricingConfig.fveBattery?.gridSubsidy || 40000;
                }

                return subsidy;
            }
            return 0;
        }

        // Helper function for FVE battery description
        function getFveDescription(data) {
            let description = '';

            if (data.fveBattery5_4) {
                description = 'FVE systém 5,4 kWp + 6,2 kWh';
            } else if (data.fveBattery7_2) {
                description = 'FVE systém 7,2 kWp + 9,3 kWh';
            } else if (data.fveBattery9_9) {
                description = 'FVE systém 9,9 kWp + 11,6 kWh';
            } else if (data.fveBattery14_4) {
                description = 'FVE systém 14,4 kWp + 15,5 kWh';
            }

            // Add wallbox info if present
            if (data.wallboxCount && data.wallboxCount > 0) {
                description += ` + ${data.wallboxCount}x Wallbox`;
            }

            return description;
        }



        // Helper function for rainwater cost calculation
        function calculateRainwaterCost(data) {
            const tankSize = data.rainwaterTankSize || 0;
            if (tankSize > 0) {
                // Cost = base cost + (tank size × cost per m³)
                const baseCost = pricingConfig.rainwater?.baseCost || 45000;
                const costPerM3 = pricingConfig.rainwater?.costPerM3 || 9000;
                return baseCost + (tankSize * costPerM3);
            }
            return 0;
        }

        // Helper function for rainwater subsidy calculation
        function calculateRainwaterSubsidy(data) {
            const tankSize = data.rainwaterTankSize || 0;
            if (tankSize > 0) {
                // Subsidy = base subsidy + (tank size × subsidy per m³)
                const baseSubsidy = pricingConfig.rainwater?.baseSubsidy || 20000;
                const subsidyPerM3 = pricingConfig.rainwater?.subsidyPerM3 || 3000;
                return baseSubsidy + (tankSize * subsidyPerM3);
            }
            return 0;
        }

        // Helper function for family bonus calculation
        function calculateFamilyBonus(data) {
            const childrenFullCare = data.childrenFullCare || 0;
            const childrenPartialCare = data.childrenPartialCare || 0;

            // Calculate total family bonus using configurable values
            const fullCareAmount = pricingConfig.bonuses?.childFullCare || 50000;
            const partialCareAmount = pricingConfig.bonuses?.childPartialCare || 25000;
            const fullCareBonus = childrenFullCare * fullCareAmount;
            const partialCareBonus = childrenPartialCare * partialCareAmount;

            return fullCareBonus + partialCareBonus;
        }

        // Debug: Log all items that will be added to PDF
        console.log('PDF Generation - CN Items to be added:');
        items.forEach((item, index) => {
            console.log(`${index + 1}. ${item.name}: ${item.cost} Kč (isSubsidy: ${item.isSubsidy || false})`);
        });

        // Calculate total of all subsidies for verification
        const totalSubsidiesFromItems = items
            .filter(item => item.isSubsidy)
            .reduce((sum, item) => sum + Math.abs(item.cost), 0);

        console.log('\n🔍 SUBSIDY BREAKDOWN ANALYSIS:');
        console.log('==============================');
        console.log('Individual subsidy components from submission:');
        console.log('- facadeSubsidy:', facadeSubsidy);
        console.log('- roofSubsidy:', roofSubsidy);
        console.log('- atticSubsidy:', atticSubsidy);
        console.log('- wallSubsidy:', wallSubsidy);
        console.log('- floorSubsidy:', floorSubsidy);
        console.log('- windowsSubsidy:', windowsSubsidy);
        console.log('- shadingSubsidy:', shadingSubsidy);
        console.log('- heatPumpSubsidy:', heatPumpSubsidy);
        console.log('- photovoltaicSubsidy:', photovoltaicSubsidy);
        console.log('- fveBatterySubsidy:', fveBatterySubsidy);
        console.log('- regionalBonusAmount:', regionalBonusAmount);
        console.log('- familyBonusAmount:', familyBonusAmount);
        console.log('- combinationBonusAmount:', combinationBonusAmount);
        console.log('- basicSupportAmount:', basicSupportAmount);

        const calculatedTotal = facadeSubsidy + roofSubsidy + atticSubsidy + wallSubsidy + floorSubsidy +
                               windowsSubsidy + shadingSubsidy + heatPumpSubsidy + photovoltaicSubsidy +
                               fveBatterySubsidy + regionalBonusAmount + familyBonusAmount +
                               combinationBonusAmount + basicSupportAmount;

        console.log('\nCalculated total from components:', calculatedTotal);
        console.log('Total subsidies from CN items:', totalSubsidiesFromItems);
        console.log('Expected total subsidy:', totalSubsidy);
        console.log('Difference (expected - CN items):', totalSubsidy - totalSubsidiesFromItems);
        console.log('Difference (expected - calculated):', totalSubsidy - calculatedTotal);

        // Sort items by CN_xxx number (extract number from CN_xxx and sort numerically)
        items.sort((a, b) => {
            const getCNNumber = (name) => {
                const match = name.match(/CN_(\d+)/);
                return match ? parseInt(match[1], 10) : 999; // Put items without CN_xxx at the end
            };
            return getCNNumber(a.name) - getCNNumber(b.name);
        });

        // Add table rows for each item
        items.forEach((item, index) => {
            const y = currentY + (index * rowHeight);

            // Item description
            doc.text(item.name, itemCol, y, { width: 350 });

            // Handle subsidies (negative values) differently
            if (item.isSubsidy) {
                // For subsidies, show as negative values
                doc.text(`-${formatNumber(Math.abs(item.cost))} Kč`, priceCol, y, { width: 80, align: 'right' });
                doc.text('-', vatCol, y, { width: 40, align: 'center' });
                doc.text(`-${formatNumber(Math.abs(item.costWithVAT))} Kč`, totalCol, y, { width: 100, align: 'right' });
            } else {
                // For regular costs
                doc.text(`${formatNumber(item.cost)} Kč`, priceCol, y, { width: 80, align: 'right' });
                doc.text(vatRate, vatCol, y, { width: 40, align: 'center' });
                doc.text(`${formatNumber(item.costWithVAT)} Kč`, totalCol, y, { width: 100, align: 'right' });
            }
        });

        console.log('Price breakdown table added to second page');
    } catch (err) {
        console.error('Error adding second page:', err);
    }

    // Add third page (price summary)
    try {
        console.log('Adding third page...');
        doc.addPage();

        // Get the full path to the compressed template image
        const imagePath3 = path.join(__dirname, 'public', 'templates', 'compressed', 'page3.jpg');
        console.log('Image path 3:', imagePath3);
        console.log('Image exists:', fs.existsSync(imagePath3));

        // Add the compressed template image as background
        doc.image(imagePath3, 0, 0, {
            width: doc.page.width,
            height: doc.page.height
        });
        console.log('Third page image added successfully');

        // Add price information aligned with template form fields
        doc.font('dejavu').fontSize(12).fillColor('#000000');

        // Format numbers with thousand separators
        const formatNumber = (num) => Math.round(num).toLocaleString('cs-CZ');

        console.log('Adding price information:');
        console.log('- Total cost:', totalCost);
        console.log('- Total cost with VAT:', totalCostWithVAT);
        console.log('- Total subsidy:', totalSubsidy);
        console.log('- Final cost:', finalCost);

        // Calculate positions based on template form fields
        // The form fields are positioned around 27% down the page
        // A4 page height is 842 points, so we need to position around y=227-320
        const priceBaseY = 180; // Starting Y position for first price field
        const priceLineHeight = 26; // Space between price fields
        const priceX = 360; // X position in the input fields (right side)
        const fieldWidth = 120; // Width of the input field area

        // Position price information to align with template form fields
        doc.text(formatNumber(totalCost), priceX, priceBaseY, { width: fieldWidth, align: 'right' });                           // Celková cena bez DPH
        doc.text(formatNumber(totalCostWithVAT), priceX, priceBaseY + priceLineHeight, { width: fieldWidth, align: 'right' }); // Celková cena vč. DPH
        doc.text(formatNumber(totalSubsidy), priceX, priceBaseY + priceLineHeight * 2, { width: fieldWidth, align: 'right' }); // Dotace
        doc.text(formatNumber(finalCost), priceX, priceBaseY + priceLineHeight * 3, { width: fieldWidth, align: 'right' });    // Celková cena s DPH po odečtení dotací
        console.log('Price information added to third page');
    } catch (err) {
        console.error('Error adding third page:', err);
    }

    // Add fourth page (additional information)
    try {
        console.log('Adding fourth page...');
        doc.addPage();

        // Get the full path to the compressed template image
        const imagePath4 = path.join(__dirname, 'public', 'templates', 'compressed', 'page4.jpg');
        console.log('Image path 4:', imagePath4);
        console.log('Image exists:', fs.existsSync(imagePath4));

        // Add the compressed template image as background
        doc.image(imagePath4, 0, 0, {
            width: doc.page.width,
            height: doc.page.height
        });
        console.log('Fourth page image added successfully');

        // This page is just the template image with no overlays for now
        // You can add additional information here if needed
    } catch (err) {
        console.error('Error adding fourth page:', err);
    }

    // End the document
    console.log('Ending document...');
    doc.end();
    console.log('Document ended');

    // Wait for PDF to be fully generated
    console.log('Waiting for PDF stream to end...');
    await new Promise((resolve) => pdfStream.on('end', resolve));
    console.log('PDF stream ended');

    console.log('Concatenating PDF chunks...');
    const pdfBuffer = Buffer.concat(pdfChunks);
    console.log('PDF buffer created, size:', pdfBuffer.length);

    return pdfBuffer;
}

module.exports = { generateTemplatePDF };
