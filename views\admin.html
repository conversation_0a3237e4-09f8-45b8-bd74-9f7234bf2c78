<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Ka<PERSON>ulačka App</title>
    <link rel="stylesheet" href="/style.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        /* Admin page styles */
        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .dashboard-header h1 {
            color: var(--primary-color);
        }

        .back-link {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
        }

        .back-link:hover {
            color: var(--primary-hover);
        }

        .user-list {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            overflow: hidden;
        }

        .user-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid #e5e7eb;
        }

        .user-item:last-child {
            border-bottom: none;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .profile-image-small {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        .user-name {
            font-weight: 500;
            margin-bottom: 3px;
        }

        .user-email {
            color: var(--text-light);
            font-size: 14px;
        }

        .role-form {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .role-select {
            padding: 8px 12px;
            border-radius: var(--border-radius);
            border: 1px solid #d1d5db;
            font-family: 'Poppins', sans-serif;
        }

        .update-role-btn {
            padding: 8px 15px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
        }

        .update-role-btn:hover {
            background-color: var(--primary-hover);
        }

        .loading-spinner {
            display: flex;
            justify-content: center;
            padding: 30px;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: var(--border-radius);
            color: white;
        }

        .alert-success {
            background-color: var(--success);
        }

        .alert-danger {
            background-color: var(--error);
        }

        /* Search styles */
        .search-container {
            margin-bottom: 20px;
        }

        .search-input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }

        .search-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #9ca3af;
            z-index: 1;
            pointer-events: none;
        }

        .search-input {
            width: 100%;
            padding: 12px 45px 12px 45px;
            padding-left: 45px !important;
            border: 1px solid #d1d5db;
            border-radius: var(--border-radius);
            font-family: 'Poppins', sans-serif;
            font-size: 16px;
            transition: var(--transition);
            box-sizing: border-box;
        }

        .search-input:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.2);
        }

        .clear-search-btn {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #9ca3af;
            cursor: pointer;
            font-size: 16px;
            display: none;
            pointer-events: auto;
        }

        .clear-search-btn:hover {
            color: var(--error);
        }

        .user-stats {
            margin-bottom: 15px;
            color: var(--text-light);
            font-size: 14px;
        }

        .highlight {
            background-color: rgba(255, 230, 0, 0.3);
            padding: 2px;
            border-radius: 2px;
        }

        @media (max-width: 768px) {
            .user-item {
                flex-direction: column;
                align-items: flex-start;
            }

            .role-form {
                margin-top: 15px;
                width: 100%;
            }

            .role-select {
                flex-grow: 1;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="dashboard-header">
            <h1>Správa uživatelů</h1>
            <a href="/dashboard" class="back-link">
                <i class="fas fa-arrow-left"></i> Zpět
            </a>
        </div>

        <div id="alertContainer"></div>

        <!-- Search Bar -->
        <div class="search-container">
            <div class="search-input-wrapper">
                <i class="fas fa-search search-icon"></i>
                <input type="text" id="userSearch" class="search-input" placeholder="Search by name or email...">
                <button id="clearSearch" class="clear-search-btn" style="display: none;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <div class="user-stats">
            <span id="userCount">0</span> users found
        </div>

        <div id="userList" class="user-list">
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin fa-2x"></i>
            </div>
        </div>
    </div>

    <script>
        // Store all users for filtering
        let allUsers = [];

        // Fetch all users
        async function fetchUsers() {
            try {
                const response = await fetch('/admin/users');
                if (!response.ok) {
                    throw new Error('Failed to fetch users');
                }

                allUsers = await response.json();
                console.log('Fetched users:', allUsers); // Debug log
                displayUsers(allUsers);
                updateUserCount(allUsers.length);
            } catch (error) {
                console.error('Error:', error);
                showAlert('Failed to load users. Please try again.', 'danger');
            }
        }

        // Display users
        function displayUsers(users, searchTerm = '') {
            const userList = document.getElementById('userList');

            if (users.length === 0) {
                userList.innerHTML = '<div class="user-item">Žádní uživatelé nenalezeni.</div>';
                return;
            }

            userList.innerHTML = '';

            users.forEach(user => {
                const userItem = document.createElement('div');
                userItem.className = 'user-item';

                // If there's a search term, highlight matching parts
                let displayName = user.displayName;
                let email = user.email;

                if (searchTerm) {
                    const regex = new RegExp(`(${escapeRegExp(searchTerm)})`, 'gi');
                    displayName = displayName.replace(regex, '<span class="highlight">$1</span>');
                    email = email.replace(regex, '<span class="highlight">$1</span>');
                }

                // Debug log for profile image
                console.log('User profile image:', user.displayName, user.profileImage);

                // Better profile image handling with Google URL fix
                function getReliableProfileImage(profileImage, displayName) {
                    if (!profileImage || profileImage.trim() === '') {
                        return getInitialsAvatar(displayName || 'User');
                    }

                    // Fix Google profile image URLs
                    if (profileImage.includes('googleusercontent.com')) {
                        const baseUrl = profileImage.split('=')[0];
                        return baseUrl + '=s96-c';
                    }

                    return profileImage;
                }

                function getInitialsAvatar(name) {
                    const initials = (name || 'User').split(' ').map(n => n[0]).join('').substring(0, 2).toUpperCase();
                    return `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&background=007bff&color=fff&size=96&bold=true`;
                }

                const profileImageSrc = getReliableProfileImage(user.profileImage, user.displayName);
                const fallbackSrc = getInitialsAvatar(user.displayName || 'User');

                userItem.innerHTML = `
                    <div class="user-info">
                        <img src="${profileImageSrc}" alt="Profile" class="profile-image-small" onerror="this.src='${fallbackSrc}'">
                        <div>
                            <p class="user-name">${displayName}</p>
                            <p class="user-email">${email}</p>
                            <p class="user-id">ID: ${user.id}</p>
                        </div>
                    </div>

                    <form class="role-form" data-user-id="${user.id}">
                        <select class="role-select" name="role">
                            <option value="guest" ${user.role === 'guest' ? 'selected' : ''}>Guest</option>
                            <option value="user" ${user.role === 'user' ? 'selected' : ''}>User</option>
                            <option value="editor" ${user.role === 'editor' ? 'selected' : ''}>Editor</option>
                            <option value="admin" ${user.role === 'admin' ? 'selected' : ''}>Admin</option>
                            <option value="smlouva1" ${user.role === 'smlouva1' ? 'selected' : ''}>Smlouva1</option>
                            <option value="smlouva2" ${user.role === 'smlouva2' ? 'selected' : ''}>Smlouva2</option>
                        </select>
                        <button type="submit" class="update-role-btn">Update</button>
                    </form>
                `;

                userList.appendChild(userItem);

                // Add event listener to form
                const form = userItem.querySelector('.role-form');
                form.addEventListener('submit', updateUserRole);
            });
        }

        // Helper function to escape special characters in regex
        function escapeRegExp(string) {
            return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        }

        // Update user count display
        function updateUserCount(count) {
            document.getElementById('userCount').textContent = count;
        }

        // Update user role
        async function updateUserRole(event) {
            event.preventDefault();

            const form = event.target;
            const userId = form.dataset.userId;
            const role = form.querySelector('.role-select').value;
            const button = form.querySelector('.update-role-btn');

            // Log the update attempt for debugging
            console.log('Attempting to update user role:', { userId, role });

            // Disable button and show loading
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

            try {
                const response = await fetch('/admin/update-role', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ userId, role }),
                });

                console.log('Response status:', response.status);
                const data = await response.json();
                console.log('Response data:', data);

                if (response.ok) {
                    showAlert('User role updated successfully!', 'success');
                    // Refresh the user list to show updated roles
                    setTimeout(() => fetchUsers(), 1000);
                } else {
                    showAlert(data.message || 'Failed to update user role.', 'danger');
                }
            } catch (error) {
                console.error('Error:', error);
                showAlert('An error occurred. Please try again.', 'danger');
            } finally {
                // Re-enable button
                button.disabled = false;
                button.textContent = 'Update';
            }
        }

        // Show alert message
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;

            alertContainer.innerHTML = '';
            alertContainer.appendChild(alert);

            // Remove alert after 3 seconds
            setTimeout(() => {
                alert.remove();
            }, 3000);
        }

        // Search functionality
        function searchUsers(searchTerm) {
            if (!searchTerm) {
                displayUsers(allUsers);
                updateUserCount(allUsers.length);
                return;
            }

            searchTerm = searchTerm.toLowerCase();
            const filteredUsers = allUsers.filter(user => {
                return (
                    user.displayName.toLowerCase().includes(searchTerm) ||
                    user.email.toLowerCase().includes(searchTerm)
                );
            });

            displayUsers(filteredUsers, searchTerm);
            updateUserCount(filteredUsers.length);
        }

        // Initialize search functionality
        function initializeSearch() {
            const searchInput = document.getElementById('userSearch');
            const clearButton = document.getElementById('clearSearch');

            // Search as user types
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.trim();
                searchUsers(searchTerm);

                // Show/hide clear button
                clearButton.style.display = searchTerm ? 'block' : 'none';
            });

            // Clear search when button is clicked
            clearButton.addEventListener('click', function() {
                searchInput.value = '';
                searchUsers('');
                this.style.display = 'none';
                searchInput.focus();
            });

            // Clear search when Escape key is pressed
            searchInput.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    searchInput.value = '';
                    searchUsers('');
                    clearButton.style.display = 'none';
                }
            });
        }

        // Load users and initialize search when page loads
        window.addEventListener('DOMContentLoaded', () => {
            fetchUsers();
            initializeSearch();
        });
    </script>
</body>
</html>
