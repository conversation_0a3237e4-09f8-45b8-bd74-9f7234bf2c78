# Brevo API Setup for Railway Email Fix

## Problem
Railway blocks SMTP connections, causing email sending to fail with `Connection timeout` errors.

## Solution
Use Brevo's REST API as a fallback when SMTP fails.

## Setup Steps

### 1. Get Your Brevo API Key
1. **Login to Brevo**: Go to https://app.brevo.com/
2. **Navigate to Settings**: Click on your profile → Settings
3. **Go to SMTP & API**: Find the "SMTP & API" section
4. **Copy API Key**: Copy your API key (starts with `xkeysib-...`)

### 2. Add API Key to Environment Variables

#### For Local Development (.env file):
Add this line to your `.env` file:
```
BREVO_API_KEY=xkeysib-your-actual-api-key-here
```

#### For Railway Deployment:
1. **Go to Railway Dashboard**: https://railway.app/
2. **Select Your Project**: Click on your PavelAplikace project
3. **Go to Variables**: Click on "Variables" tab
4. **Add New Variable**:
   - **Name**: `BREVO_API_KEY`
   - **Value**: `xkeysib-your-actual-api-key-here`
5. **Save**: Click "Add" to save the variable

### 3. Test the Setup

#### Local Testing:
```bash
node test-email-service.js
```

#### Check Railway Logs:
After deployment, check Railway logs to see:
- ✅ SMTP verification (if working locally)
- ❌ SMTP failure (expected on Railway)
- ✅ API fallback success

## How It Works

The new EmailService automatically:

1. **Tries SMTP first** (works locally, blocked on Railway)
2. **Falls back to Brevo API** (works everywhere, including Railway)
3. **Provides consistent results** regardless of method used

## Expected Behavior

### Local Development:
- ✅ SMTP works → Uses SMTP
- 📧 Email sent via SMTP

### Railway Production:
- ❌ SMTP blocked → Connection timeout
- ✅ API fallback → Uses Brevo REST API
- 📧 Email sent via API

## Verification

After setup, you should see in Railway logs:
```
EmailService: Trying SMTP...
❌ EmailService: SMTP failed: Connection timeout
EmailService: Trying Brevo API fallback...
✅ EmailService: Brevo API successful
✅ Email with PDF(s) sent successfully to: <EMAIL>
```

## Benefits

- ✅ **Works on Railway** (no more connection timeouts)
- ✅ **Works locally** (still uses SMTP when available)
- ✅ **Automatic fallback** (no manual intervention needed)
- ✅ **Same functionality** (attachments, HTML, etc.)
- ✅ **Better reliability** (two methods instead of one)

## Troubleshooting

### "API key not configured" error:
- Check that `BREVO_API_KEY` is set in Railway variables
- Verify the API key starts with `xkeysib-`
- Make sure there are no extra spaces in the key

### "API request failed" error:
- Check your Brevo account is active
- Verify the API key has transactional email permissions
- Check Brevo dashboard for any account issues

### Still getting SMTP errors:
- This is expected on Railway - the system should automatically fallback to API
- Check that the API fallback is working in the logs
- If API also fails, check the API key configuration
