const nodemailer = require('nodemailer');
const axios = require('axios');
const fs = require('fs');

/**
 * Email Service with SMTP fallback to Brevo API
 * Handles Railway SMTP blocking by using Brevo's REST API as fallback
 */
class EmailService {
    constructor() {
        this.setupSMTP();
        this.setupBrevoAPI();
    }

    setupSMTP() {
        // SMTP Configuration (works locally, blocked on Railway)
        const transporterConfig = {
            host: 'smtp-relay.brevo.com',
            port: process.env.NODE_ENV === 'production' ? 465 : 587,
            secure: process.env.NODE_ENV === 'production' ? true : false,
            auth: {
                user: process.env.BREVO_SMTP_USER,
                pass: process.env.BREVO_SMTP_PASS
            },
            connectionTimeout: 30000,
            greetingTimeout: 15000,
            socketTimeout: 30000,
            pool: false,
            maxConnections: 1,
            tls: {
                rejectUnauthorized: false,
                minVersion: 'TLSv1.2',
                maxVersion: 'TLSv1.3'
            },
            debug: process.env.NODE_ENV !== 'production',
            logger: process.env.NODE_ENV !== 'production'
        };

        this.transporter = nodemailer.createTransport(transporterConfig);
    }

    setupBrevoAPI() {
        // Brevo API Configuration using direct HTTP requests (works everywhere, including Railway)
        this.brevoApiUrl = 'https://api.brevo.com/v3/smtp/email';
        this.brevoApiKey = process.env.BREVO_API_KEY;
    }

    /**
     * Send email with smart routing: SMTP locally, API in production
     */
    async sendEmail(mailOptions) {
        console.log('EmailService: Attempting to send email...');
        console.log('To:', mailOptions.to);
        console.log('Subject:', mailOptions.subject);
        console.log('Attachments:', mailOptions.attachments ? mailOptions.attachments.length : 0);

        // Smart routing: Skip SMTP in production (Railway) since it's always blocked
        const isProduction = process.env.NODE_ENV === 'production';
        const hasApiKey = !!this.brevoApiKey;

        if (isProduction && hasApiKey) {
            // Production: Use API directly (Railway blocks SMTP)
            console.log('EmailService: Production environment detected, using Brevo API directly...');
            try {
                const result = await this.sendViaBrevoAPI(mailOptions);
                console.log('✅ EmailService: Brevo API successful');
                return result;
            } catch (apiError) {
                console.error('❌ EmailService: Brevo API failed:', apiError.message);
                throw new Error(`Email sending failed: API (${apiError.message})`);
            }
        } else {
            // Development: Try SMTP first, fallback to API
            try {
                console.log('EmailService: Development environment, trying SMTP first...');
                const result = await this.sendViaSMTP(mailOptions);
                console.log('✅ EmailService: SMTP successful');
                return result;
            } catch (smtpError) {
                console.log('❌ EmailService: SMTP failed:', smtpError.message);

                if (hasApiKey) {
                    // Fallback to API
                    try {
                        console.log('EmailService: Trying Brevo API fallback...');
                        const result = await this.sendViaBrevoAPI(mailOptions);
                        console.log('✅ EmailService: Brevo API successful');
                        return result;
                    } catch (apiError) {
                        console.error('❌ EmailService: Both SMTP and API failed');
                        console.error('SMTP Error:', smtpError.message);
                        console.error('API Error:', apiError.message);
                        throw new Error(`Email sending failed: SMTP (${smtpError.message}), API (${apiError.message})`);
                    }
                } else {
                    // No API key, can't fallback
                    throw new Error(`Email sending failed: SMTP (${smtpError.message}), no API key for fallback`);
                }
            }
        }
    }

    /**
     * Send via SMTP (nodemailer)
     */
    async sendViaSMTP(mailOptions) {
        // Shorter timeout in production since SMTP is likely blocked
        const timeoutMs = process.env.NODE_ENV === 'production' ? 5000 : 30000; // 5s in prod, 30s in dev
        const emailPromise = this.transporter.sendMail(mailOptions);
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('SMTP timeout')), timeoutMs);
        });

        return await Promise.race([emailPromise, timeoutPromise]);
    }

    /**
     * Send via Brevo API (REST)
     */
    async sendViaBrevoAPI(mailOptions) {
        // Convert nodemailer format to Brevo API format
        const brevoEmail = {
            sender: {
                name: "Kalkulace",
                email: "<EMAIL>"
            },
            to: [
                {
                    email: mailOptions.to,
                    name: mailOptions.to.split('@')[0] // Use email prefix as name
                }
            ],
            subject: mailOptions.subject,
            htmlContent: mailOptions.html || `<p>${mailOptions.text}</p>`,
            textContent: mailOptions.text
        };

        // Handle attachments
        if (mailOptions.attachments && mailOptions.attachments.length > 0) {
            brevoEmail.attachment = [];
            
            for (const attachment of mailOptions.attachments) {
                let content;
                
                if (attachment.content) {
                    // Content is already a buffer
                    content = attachment.content.toString('base64');
                } else if (attachment.path) {
                    // Read file from path
                    const fileBuffer = fs.readFileSync(attachment.path);
                    content = fileBuffer.toString('base64');
                } else {
                    console.warn('EmailService: Skipping attachment without content or path');
                    continue;
                }

                brevoEmail.attachment.push({
                    name: attachment.filename || 'attachment.pdf',
                    content: content
                });
            }
        }

        console.log('EmailService: Sending via Brevo API...');
        console.log('API Email structure:', {
            to: brevoEmail.to[0].email,
            subject: brevoEmail.subject,
            attachmentCount: brevoEmail.attachment ? brevoEmail.attachment.length : 0
        });
        console.log('API Key configured:', !!this.brevoApiKey);
        console.log('API Key prefix:', this.brevoApiKey ? this.brevoApiKey.substring(0, 15) + '...' : 'Not set');

        try {
            const response = await axios.post(this.brevoApiUrl, brevoEmail, {
                headers: {
                    'accept': 'application/json',
                    'api-key': this.brevoApiKey,
                    'content-type': 'application/json'
                },
                timeout: 30000 // 30 second timeout
            });

            console.log('✅ Brevo API response:', response.data);

            return {
                messageId: response.data.messageId,
                response: 'Brevo API: Email sent successfully',
                accepted: [mailOptions.to],
                rejected: []
            };
        } catch (apiError) {
            console.error('❌ Brevo API detailed error:');
            console.error('Status:', apiError.response?.status);
            console.error('Status Text:', apiError.response?.statusText);
            console.error('Response Data:', apiError.response?.data);
            console.error('Error Message:', apiError.message);
            throw new Error(`Brevo API error: ${apiError.response?.status} ${apiError.response?.statusText || apiError.message}`);
        }
    }

    /**
     * Verify email configuration
     */
    async verify() {
        try {
            // Try SMTP verification first
            await this.transporter.verify();
            console.log('✅ EmailService: SMTP verification successful');
            return true;
        } catch (smtpError) {
            console.log('❌ EmailService: SMTP verification failed, checking API...');
            
            // Check if Brevo API key is configured
            if (!this.brevoApiKey) {
                throw new Error('Neither SMTP nor Brevo API key is configured');
            }

            console.log('✅ EmailService: Brevo API key configured, fallback available');
            return true;
        }
    }
}

module.exports = new EmailService();
