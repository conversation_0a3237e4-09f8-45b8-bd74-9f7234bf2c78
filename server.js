const express = require('express');
const path = require('path');
const nodemailer = require('nodemailer');
const session = require('express-session');
const fs = require('fs');
const passport = require('passport');
const dotenv = require('dotenv');
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const compression = require('compression');
const { generateTemplatePDF } = require('./template-pdf');
const performanceMonitor = require('./performance-monitor');

// Load environment variables
dotenv.config();

// Configure Brevo SMTP transporter with Railway-optimized settings
const transporterConfig = {
    host: 'smtp-relay.brevo.com',
    port: process.env.NODE_ENV === 'production' ? 465 : 587, // Use port 465 (SSL) for Railway
    secure: process.env.NODE_ENV === 'production' ? true : false, // SSL in production, TLS in development
    auth: {
        user: process.env.BREVO_SMTP_USER,
        pass: process.env.BREVO_SMTP_PASS
    },
    // Additional options to ensure sender name is preserved
    defaults: {
        from: '"Kalkulace" <<EMAIL>>'
    },
    // Railway-optimized timeout and connection settings
    connectionTimeout: process.env.NODE_ENV === 'production' ? 30000 : 60000, // Shorter timeout for Railway
    greetingTimeout: process.env.NODE_ENV === 'production' ? 15000 : 30000, // Shorter greeting timeout
    socketTimeout: process.env.NODE_ENV === 'production' ? 30000 : 60000, // Shorter socket timeout
    // Disable pooling in production to avoid connection issues
    pool: process.env.NODE_ENV === 'production' ? false : true,
    maxConnections: 1, // Single connection for Railway stability
    maxMessages: 10, // Reduced for Railway
    // Railway-specific TLS settings
    tls: {
        rejectUnauthorized: false, // More permissive for Railway
        ciphers: 'SSLv3'
    },
    debug: process.env.NODE_ENV !== 'production', // Enable debug in development only
    logger: process.env.NODE_ENV !== 'production' // Enable logging in development only
};

console.log('Email transporter configuration:', {
    host: transporterConfig.host,
    port: transporterConfig.port,
    user: transporterConfig.auth.user ? 'Set' : 'Not set',
    pass: transporterConfig.auth.pass ? 'Set' : 'Not set',
    environment: process.env.NODE_ENV || 'development',
    connectionTimeout: transporterConfig.connectionTimeout,
    socketTimeout: transporterConfig.socketTimeout
});

// Create transporter with fallback configurations for Railway
let transporter = nodemailer.createTransport(transporterConfig);

// Alternative SMTP configurations for Railway fallback
const alternativeConfigs = [
    {
        name: 'Brevo Port 2587',
        config: {
            ...transporterConfig,
            port: 2587,
            secure: false
        }
    },
    {
        name: 'Brevo Port 25',
        config: {
            ...transporterConfig,
            port: 25,
            secure: false,
            tls: { rejectUnauthorized: false }
        }
    }
];

// Function to create transporter with fallback
async function createTransporterWithFallback() {
    // Try primary configuration first
    try {
        await transporter.verify();
        console.log('✅ Primary SMTP configuration working');
        return transporter;
    } catch (error) {
        console.log('❌ Primary SMTP configuration failed:', error.message);

        // Try alternative configurations
        for (const alt of alternativeConfigs) {
            try {
                console.log(`Trying alternative: ${alt.name}...`);
                const altTransporter = nodemailer.createTransport(alt.config);
                await altTransporter.verify();
                console.log(`✅ Alternative SMTP configuration working: ${alt.name}`);
                return altTransporter;
            } catch (altError) {
                console.log(`❌ Alternative ${alt.name} failed:`, altError.message);
            }
        }

        // If all fail, return original transporter and let it fail later
        console.log('⚠️ All SMTP configurations failed, using primary config');
        return transporter;
    }
}

// Verify email configuration at startup with fallback
async function verifyEmailConfig() {
    try {
        console.log('Verifying email configuration...');
        transporter = await createTransporterWithFallback();
        console.log('✅ Email configuration verified successfully');
        return true;
    } catch (error) {
        console.error('❌ Email configuration verification failed:');
        console.error('Error type:', error.constructor.name);
        console.error('Error message:', error.message);
        console.error('Error code:', error.code);
        console.error('This may cause email sending to fail in production');
        return false;
    }
}

const app = express();

// Trust proxy for Railway deployment (fixes HTTPS detection)
if (process.env.NODE_ENV === 'production') {
  app.set('trust proxy', 1);
}

// Connect to MongoDB (commented out for now)
/*
mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/calculator-app', {
    useNewUrlParser: true,
    useUnifiedTopology: true
}).then(() => {
    console.log('Connected to MongoDB');
}).catch(err => {
    console.error('MongoDB connection error:', err);
});
*/

// For now, we'll use a simple in-memory store for sessions

// Configure Passport
require('./config/passport');

// Import Firebase session store
const FirebaseSessionStore = require('./middleware/firebase-session-store');

// Session configuration with Firebase store
const sessionStore = new FirebaseSessionStore({
    collection: 'sessions',
    ttl: 24 * 60 * 60 // 24 hours in seconds
});

app.use(session({
    store: sessionStore,
    secret: process.env.SESSION_SECRET || 'secret-key',
    resave: false,
    saveUninitialized: false,
    cookie: {
        maxAge: 24 * 60 * 60 * 1000, // 1 day
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production', // Use secure cookies in production
        sameSite: 'lax' // Allow same-site requests including AJAX
    }
}));

// Initialize Passport
app.use(passport.initialize());
app.use(passport.session());

// Performance monitoring
app.use(performanceMonitor.requestTimer);

// Compression middleware for better performance
app.use(compression());

// Security middleware
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com", "https://cdnjs.cloudflare.com"],
            scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'", "https://code.jquery.com", "https://cdnjs.cloudflare.com"],
            fontSrc: ["'self'", "https://fonts.gstatic.com", "https://cdnjs.cloudflare.com"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'"],
            // Allow inline event handlers temporarily
            scriptSrcAttr: ["'unsafe-inline'"]
        }
    },
    crossOriginEmbedderPolicy: false // Disable for compatibility
}));

// Optimized rate limiting - more lenient for navigation, strict for API calls
const generalLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 200, // Increased limit for better UX
    message: 'Příliš mnoho požadavků z této IP adresy. Zkuste to prosím později.',
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) => {
        // Skip rate limiting for static files
        return req.url.match(/\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/);
    }
});

// Log only important requests (not static files) for debugging
app.use((req, res, next) => {
    // Only log non-static file requests and errors in development
    if (process.env.NODE_ENV !== 'production' &&
        !req.url.match(/\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/)) {
        console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
        if (req.method === 'POST') {
            console.log('POST request body keys:', Object.keys(req.body || {}));
        }
    }
    next();
});

// Apply rate limiting to non-static requests
app.use(generalLimiter);

// Stricter rate limiting for auth routes
const authLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 10, // Limit each IP to 10 auth requests per windowMs (increased from 5)
    message: 'Příliš mnoho pokusů o přihlášení. Zkuste to prosím za 15 minut.',
    skipSuccessfulRequests: true, // Don't count successful logins against the limit
});

// Additional security headers
app.use((req, res, next) => {
    // Additional custom headers
    res.setHeader('X-Powered-By', 'Secure-App'); // Hide Express
    next();
});

// Middleware to parse JSON request bodies
app.use(express.json({ limit: '10mb' })); // Add size limit
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Optimized static file serving with caching
const staticOptions = {
    maxAge: process.env.NODE_ENV === 'production' ? '1d' : '0', // Cache for 1 day in production
    etag: true,
    lastModified: true,
    setHeaders: (res, path) => {
        // Set cache headers for different file types
        if (path.endsWith('.css') || path.endsWith('.js')) {
            res.setHeader('Cache-Control', 'public, max-age=86400'); // 1 day
        } else if (path.match(/\.(png|jpg|jpeg|gif|ico|svg)$/)) {
            res.setHeader('Cache-Control', 'public, max-age=604800'); // 1 week
        }
    }
};

// Serve static files with optimized order (most specific first)
app.use('/uploads', express.static(path.join(__dirname, 'public/uploads'), staticOptions));
app.use(express.static('public', staticOptions));
app.use(express.static('views', staticOptions));
app.use(express.static(path.join(__dirname), staticOptions));

// Import auth middleware and Firebase
const { ensureAuth, checkRole } = require('./middleware/auth');
const { admin, db } = require('./firebase-admin');

// Import document service for cleanup
const documentService = require('./services/documentService');





// Function to generate and send PDF
async function generateAndSendPDF(submission) {
    console.log('=== generateAndSendPDF called ===');
    console.log('Submission data:', {
        id: submission.id,
        name: submission.name,
        email: submission.userEmail,
        type: submission.type
    });

    try {
        // Generate PDF using template
        console.log('Generating PDF using template...');
        const pdfBuffer = await generateTemplatePDF(submission);
        console.log('PDF generated successfully, buffer size:', pdfBuffer ? pdfBuffer.length : 'null');

    // Get email from logged user and name from submission
    const email = submission.userEmail; // Use logged-in user's email
    const { name } = submission;

    // Check if it's an insulation calculator submission
    const isInsulationCalculator = submission.type === 'insulation-calculator';

    // Prepare email content based on submission type
    let emailSubject, emailText, emailHtml, pdfFilename;

    if (isInsulationCalculator) {
        emailSubject = 'Kalkulace zateplení - potvrzení';
        emailText = `Vážený/á ${name},\n\nV příloze najdete PDF s kalkulací zateplení a smlouvu o projektové dokumentaci.\n\nDěkujeme za Váš zájem,\nVáš tým`;
        emailHtml = `<p>Vážený/á ${name},</p><p>V příloze najdete PDF s kalkulací zateplení a smlouvu o projektové dokumentaci.</p><p>Děkujeme za Váš zájem,<br>Váš tým</p>`;
        pdfFilename = 'kalkulace-zatepleni.pdf';
    } else {
        emailSubject = 'Form Submission Confirmation with PDF';
        emailText = `Hello ${name},\n\nPlease find attached your confirmation PDF.\n\nThank you,\nYour App Team`;
        emailHtml = `<p>Hello ${name},</p><p>Please find attached your confirmation PDF.</p><p>Thank you,<br>Your App Team</p>`;
        pdfFilename = 'confirmation.pdf';
    }

    // Prepare attachments array
    const attachments = [
        {
            filename: pdfFilename,
            content: pdfBuffer,
            contentType: 'application/pdf'
        }
    ];

    // Generate Word document PDF if it's an insulation calculator submission
    if (isInsulationCalculator) {
        try {
            const documentService = require('./services/documentService');

            // Prepare form data for Word template
            const formData = {
                customerName: submission.name || '',
                customerAddress: submission.address || '',
                dateOfBirth: submission.dateOfBirth || '',
                email: submission.email || '',
                phone: submission.phone || '',
                propertyAddress: submission.propertyAddress || submission.address || '',
                realizationAddress: submission.realizationAddress || submission.address || '',
                totalCost: submission.totalCost || 0,
                totalCostWithVAT: submission.totalCostWithVAT || 0,
                totalSubsidy: submission.totalSubsidy || 0,
                finalCost: submission.finalCost || 0,
                facadeSubsidizedArea: submission.facadeAreaSubsidized || 0,
                facadeNonSubsidizedArea: submission.facadeAreaNonSubsidized || 0,
                facadePolystyrene: submission.materialPolystyrene || false,
                facadeMineralWool: submission.materialMineralWool || false,
                heatPump8kW: submission.heatPump8kW || false,
                heatPump12kW: submission.heatPump12kW || false,
                heatPump16kW: submission.heatPump16kW || false,
                heatPump22kW: submission.heatPump22kW || false,
                heatPumpHeatingOnly: submission.heatPumpHeatingOnly || false,
                heatPumpHeatingWater: submission.heatPumpHeatingWater || false,
                roofTypeSloped: submission.roofTypeSloped || false,
                roofTypeFlat: submission.roofTypeFlat || false,
                newBoilerYes: submission.newBoilerYes || false,
                newBoilerNo: submission.newBoilerNo || false,
                wallSdkCladding: submission.wallSdkCladding || false,
                wallPlastering: submission.wallPlastering || false
            };

            // Generate contract PDF using image templates
            const outputName = `contract_${submission.id}_${Date.now()}`;
            const userRole = submission.userRole || 'user'; // Get user role from submission
            const result = await documentService.generateImageBasedContract(outputName, formData, userRole);

            // Only log if there's an error or in development mode
            if (!result.success || process.env.NODE_ENV !== 'production') {
                console.log('Document generation result:', result.success ? 'Success' : 'Failed');
                if (!result.success) {
                    console.log('Error details:', result.error);
                }
            }

            if (result.success) {
                const fs = require('fs');

                // Try to attach PDF first, fallback to DOCX if PDF failed
                if (result.pdfPath && fs.existsSync(result.pdfPath)) {
                    try {
                        const contractPdfBuffer = await fs.promises.readFile(result.pdfPath);

                        // Add contract PDF to attachments
                        attachments.push({
                            filename: 'smlouva-projektova-dokumentace.pdf',
                            content: contractPdfBuffer,
                            contentType: 'application/pdf'
                        });

                        if (process.env.NODE_ENV !== 'production') {
                            console.log('Contract PDF added to email attachments');
                        }
                    } catch (readError) {
                        console.error('Error reading PDF file:', readError);
                    }
                } else if (process.env.NODE_ENV !== 'production') {
                    console.log('PDF file not found, checking alternatives...');
                    if (result.htmlPath && fs.existsSync(result.htmlPath)) {
                        console.log('HTML fallback available');
                    } else {
                        console.log('No valid contract files generated');
                    }
                }
            } else {
                console.error('Document generation failed - result.success is false');
                console.error('Error details:', result.error);
                console.error('Error type:', result.errorType);
            }
        } catch (error) {
            console.error('Error generating contract PDF:', error);
            // Continue with email sending even if contract PDF fails
        }
    }

    const mailOptions = {
        from: '"Kalkulace" <<EMAIL>>',
        to: email,
        subject: emailSubject,
        text: emailText,
        html: emailHtml,
        attachments: attachments,
        // Additional headers to ensure sender name is preserved
        headers: {
            'X-Sender': 'Kalkulace',
            'Reply-To': '"Kalkulace" <<EMAIL>>',
            'From': '"Kalkulace" <<EMAIL>>'
        }
    };

    console.log('Attempting to send email with options:', {
        from: mailOptions.from,
        to: mailOptions.to,
        subject: mailOptions.subject,
        attachmentCount: mailOptions.attachments ? mailOptions.attachments.length : 0,
        totalAttachmentSize: mailOptions.attachments ?
            mailOptions.attachments.reduce((total, att) => total + (att.content ? att.content.length : 0), 0) : 0
    });

    // Add timeout wrapper for email sending (longer timeout for large attachments)
    const timeoutMs = process.env.NODE_ENV === 'production' ? 180000 : 60000; // 1 minute in development

    console.log(`Sending email (with ${timeoutMs/1000}s timeout)...`);
    console.log('Environment:', process.env.NODE_ENV || 'development');
    console.log('SMTP User configured:', !!process.env.BREVO_SMTP_USER);
    console.log('SMTP Pass configured:', !!process.env.BREVO_SMTP_PASS);

    // Retry logic for Railway connection issues
    let result;
    let lastError;
    const maxRetries = process.env.NODE_ENV === 'production' ? 3 : 1;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            console.log(`Email sending attempt ${attempt}/${maxRetries}...`);
            const emailPromise = transporter.sendMail(mailOptions);
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error(`Email sending timeout after ${timeoutMs/1000} seconds`)), timeoutMs);
            });

            result = await Promise.race([emailPromise, timeoutPromise]);
            console.log(`✅ Email sent successfully on attempt ${attempt}`);
            break; // Success, exit retry loop

        } catch (error) {
            lastError = error;
            console.log(`❌ Email sending attempt ${attempt} failed:`, error.message);

            if (attempt < maxRetries) {
                const retryDelay = attempt * 2000; // 2s, 4s, 6s delays
                console.log(`Retrying in ${retryDelay/1000} seconds...`);
                await new Promise(resolve => setTimeout(resolve, retryDelay));
            }
        }
    }

    if (!result) {
        throw lastError || new Error('Email sending failed after all retry attempts');
    }

    console.log('✅ Email with PDF(s) sent via Brevo SMTP to:', email);
    console.log('Email send result:', {
        messageId: result.messageId,
        response: result.response,
        accepted: result.accepted,
        rejected: result.rejected
    });

    // Update the submission in Firestore
    await db.collection('submissions').doc(submission.id).update({
        emailSent: true,
        emailSentAt: admin.firestore.FieldValue.serverTimestamp()
    });

    return true;
    } catch (error) {
        console.error('Error in generateAndSendPDF:', error);
        console.error('Error details:', {
            message: error.message,
            stack: error.stack,
            code: error.code
        });
        throw error; // Re-throw to be caught by the calling function
    }
}

// Import routes
const authRoutes = require('./routes/auth');
const indexRoutes = require('./routes/index');
const adminRoutes = require('./routes/admin');
const submissionsRoutes = require('./routes/submissions');
const logoRoutes = require('./routes/logo');
const pricingRoutes = require('./routes/pricing');

// Debug PDF routes
app.get('/debug-template-pdf/:id', ensureAuth, checkRole(['editor', 'admin']), async (req, res) => {
    try {
        const submissionId = req.params.id;
        console.log(`Generating template PDF for submission ${submissionId}`);

        // Get the submission from Firestore
        const submissionDoc = await db.collection('submissions').doc(submissionId).get();

        if (!submissionDoc.exists) {
            return res.status(404).send('Submission not found');
        }

        const submission = {
            id: submissionDoc.id,
            ...submissionDoc.data()
        };

        // Generate PDF using template
        const pdfBuffer = await generateTemplatePDF(submission);

        // Set response headers
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', `inline; filename="template-${submissionId}.pdf"`);

        // Send the PDF
        res.send(pdfBuffer);
    } catch (error) {
        console.error('Error generating template PDF:', error);
        res.status(500).send('Error generating PDF');
    }
});





// View contract PDF route
app.get('/view-contract-pdf/:id', ensureAuth, checkRole(['editor', 'admin']), async (req, res) => {
    try {
        const submissionId = req.params.id;
        console.log(`Viewing contract PDF for submission ${submissionId}`);

        // Get the submission from Firestore
        const submissionDoc = await db.collection('submissions').doc(submissionId).get();

        if (!submissionDoc.exists) {
            return res.status(404).send('Submission not found');
        }

        const submission = {
            id: submissionDoc.id,
            ...submissionDoc.data()
        };

        // Check if contract document already exists
        if (submission.generatedDocument) {
            const fs = require('fs');

            // Check for PDF first
            if (submission.generatedDocument.pdfPath && fs.existsSync(submission.generatedDocument.pdfPath)) {
                console.log('Using existing contract PDF:', submission.generatedDocument.pdfPath);

                // Set response headers
                res.setHeader('Content-Type', 'application/pdf');
                res.setHeader('Content-Disposition', `inline; filename="contract-${submissionId}.pdf"`);

                // Send the existing PDF
                return res.sendFile(submission.generatedDocument.pdfPath);
            }

            // Check for HTML fallback
            if (submission.generatedDocument.htmlPath && fs.existsSync(submission.generatedDocument.htmlPath)) {
                console.log('Using existing contract HTML:', submission.generatedDocument.htmlPath);

                // Set response headers
                res.setHeader('Content-Type', 'text/html');
                res.setHeader('Content-Disposition', `inline; filename="contract-${submissionId}.html"`);

                // Send the existing HTML
                return res.sendFile(submission.generatedDocument.htmlPath);
            }
        }

        // If no existing PDF, generate a new one
        console.log('No existing contract PDF found, generating new one...');
        const documentService = require('./services/documentService');

        // Prepare form data for Word template
        const formData = {
            customerName: submission.name || '',
            customerAddress: submission.address || '',
            dateOfBirth: submission.dateOfBirth || '',
            email: submission.email || '',
            phone: submission.phone || '',
            propertyAddress: submission.propertyAddress || submission.address || '',
            realizationAddress: submission.realizationAddress || submission.address || '',
            totalCost: submission.totalCost || 0,
            totalCostWithVAT: submission.totalCostWithVAT || 0,
            totalSubsidy: submission.totalSubsidy || 0,
            finalCost: submission.finalCost || 0,
            facadeSubsidizedArea: submission.facadeAreaSubsidized || 0,
            facadeNonSubsidizedArea: submission.facadeAreaNonSubsidized || 0,
            facadePolystyrene: submission.materialPolystyrene || false,
            facadeMineralWool: submission.materialMineralWool || false,
            heatPump8kW: submission.heatPump8kW || false,
            heatPump12kW: submission.heatPump12kW || false,
            heatPump16kW: submission.heatPump16kW || false,
            heatPump22kW: submission.heatPump22kW || false,
            heatPumpHeatingOnly: submission.heatPumpHeatingOnly || false,
            heatPumpHeatingWater: submission.heatPumpHeatingWater || false,
            roofTypeSloped: submission.roofTypeSloped || false,
            roofTypeFlat: submission.roofTypeFlat || false,
            newBoilerYes: submission.newBoilerYes || false,
            newBoilerNo: submission.newBoilerNo || false,
            wallSdkCladding: submission.wallSdkCladding || false,
            wallPlastering: submission.wallPlastering || false
        };

        // Generate contract PDF using image templates
        const customerName = formData.customerName || 'Klient';
        // Keep Czech characters and basic Latin characters, remove only problematic file system characters
        const sanitizedName = customerName
            .replace(/[<>:"/\\|?*]/g, '') // Remove file system forbidden characters
            .replace(/\s+/g, '_') // Replace spaces with underscores
            .trim();
        const outputName = `Smlouva_Projektova_dokumentace_${sanitizedName}`;

        console.log('Generating contract using image templates...');
        // Use the ORIGINAL SUBMITTER's role for template selection (not current viewer's role)
        const userRole = submission.userRole || 'user';
        console.log('Original submitter role for contract template selection:', userRole);
        console.log('Current viewer role (NOT used):', req.user.role);
        const result = await documentService.generateImageBasedContract(outputName, formData, userRole);

        console.log('Contract generation result:', result);
        console.log('Result success:', result.success);
        console.log('Result message:', result.message);
        console.log('Result pdfPath:', result.pdfPath);

        if (result.success) {
            // Update submission with generated document info
            await db.collection('submissions').doc(submissionId).update({
                generatedDocument: {
                    templateName: 'smlouva-image',
                    pdfPath: result.pdfPath || null,
                    generatedAt: admin.firestore.FieldValue.serverTimestamp(),
                    generatedBy: req.user.id,
                    generatedByName: req.user.displayName,
                    pdfError: result.pdfError || null,
                    message: result.message || null
                }
            });

            // If PDF was generated successfully, send PDF
            if (result.pdfPath && require('fs').existsSync(result.pdfPath)) {
                console.log('Sending PDF file:', result.pdfPath);
                // Set response headers for PDF
                res.setHeader('Content-Type', 'application/pdf');
                res.setHeader('Content-Disposition', `inline; filename="contract-${submissionId}.pdf"`);

                // Send the PDF
                res.sendFile(result.pdfPath);
            } else if (result.htmlPath && require('fs').existsSync(result.htmlPath)) {
                // HTML fallback was used, send HTML file
                console.log('PDF not available, sending HTML contract file:', result.htmlPath);
                res.setHeader('Content-Type', 'text/html');
                res.setHeader('Content-Disposition', `inline; filename="contract-${submissionId}.html"`);

                // Send the HTML
                res.sendFile(result.htmlPath);

            } else {
                console.error('No valid file generated or accessible');
                console.log('File existence check:');
                if (result.pdfPath) {
                    console.log('- PDF path:', result.pdfPath, 'exists:', require('fs').existsSync(result.pdfPath));
                }
                if (result.htmlPath) {
                    console.log('- HTML path:', result.htmlPath, 'exists:', require('fs').existsSync(result.htmlPath));
                }
                if (result.docxPath) {
                    console.log('- DOCX path:', result.docxPath, 'exists:', require('fs').existsSync(result.docxPath));
                }
                res.status(500).send('Error: Contract document was generated but files are not accessible');
            }
        } else {
            console.error('Contract generation failed');
            console.error('Error details:', result.error);
            console.error('Error type:', result.errorType);
            console.error('PDF error:', result.pdfError);
            res.status(500).send(`Error generating contract document: ${result.error || 'Unknown error'}`);
        }
    } catch (error) {
        console.error('Error viewing contract PDF:', error);
        console.error('Error stack:', error.stack);
        res.status(500).send(`Error generating contract PDF: ${error.message}`);
    }
});

// Send approved email route
app.post('/send-approved-email', ensureAuth, checkRole(['editor', 'admin']), async (req, res) => {
    try {
        const { submissionId } = req.body;
        console.log(`Sending approved email for submission ${submissionId}`);
        console.log('Request body:', req.body);
        console.log('submissionId type:', typeof submissionId);
        console.log('submissionId value:', submissionId);

        // Validate submissionId
        if (!submissionId || typeof submissionId !== 'string' || submissionId.trim() === '') {
            console.error('Invalid submissionId:', submissionId);
            return res.status(400).json({ message: 'Invalid submission ID' });
        }

        // Get the submission from Firestore
        const submissionDoc = await db.collection('submissions').doc(submissionId).get();

        if (!submissionDoc.exists) {
            return res.status(404).json({ message: 'Submission not found' });
        }

        const submission = {
            id: submissionId,
            ...submissionDoc.data()
        };

        // Send the email
        await generateAndSendPDF(submission);

        // Update the submission in Firestore
        await db.collection('submissions').doc(submissionId).update({
            emailSent: true,
            emailSentAt: admin.firestore.FieldValue.serverTimestamp(),
            emailSentBy: req.user.id,
            emailSentByName: req.user.displayName
        });

        res.json({ message: 'Email sent successfully' });
    } catch (error) {
        console.error('Error sending approved email:', error);
        res.status(500).json({ message: 'Error sending email' });
    }
});



// Memory monitoring endpoints (for debugging)
app.get('/api/memory-status', ensureAuth, checkRole(['admin']), async (req, res) => {
    try {
        const memoryStats = await documentService.getMemoryStatistics();
        const currentSnapshot = await documentService.getMemorySnapshot();

        res.json({
            current: currentSnapshot,
            statistics: memoryStats,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('Error getting memory status:', error);
        res.status(500).json({ error: 'Failed to get memory status' });
    }
});

// Memory snapshot endpoint
app.get('/api/memory-snapshot', ensureAuth, checkRole(['admin']), async (req, res) => {
    try {
        const snapshot = await documentService.getMemorySnapshot();
        res.json(snapshot);
    } catch (error) {
        console.error('Error getting memory snapshot:', error);
        res.status(500).json({ error: 'Failed to get memory snapshot' });
    }
});

// Force memory summary log
app.post('/api/memory-log', ensureAuth, checkRole(['admin']), (req, res) => {
    try {
        documentService.logMemorySummary();
        res.json({ message: 'Memory summary logged to console' });
    } catch (error) {
        console.error('Error logging memory summary:', error);
        res.status(500).json({ error: 'Failed to log memory summary' });
    }
});

// Test email endpoint for Railway debugging
app.post('/api/test-email', ensureAuth, checkRole(['admin']), async (req, res) => {
    try {
        console.log('=== EMAIL TEST ENDPOINT ===');
        console.log('Environment:', process.env.NODE_ENV || 'development');
        console.log('SMTP User:', process.env.BREVO_SMTP_USER ? 'Set' : 'Not set');
        console.log('SMTP Pass:', process.env.BREVO_SMTP_PASS ? 'Set' : 'Not set');

        const testMailOptions = {
            from: '"Kalkulace Test" <<EMAIL>>',
            to: req.user.email,
            subject: 'Test Email - Railway Deployment',
            text: 'This is a test email to verify Railway email configuration is working.',
            html: '<p>This is a test email to verify Railway email configuration is working.</p><p>Environment: ' + (process.env.NODE_ENV || 'development') + '</p>'
        };

        console.log('Sending test email to:', req.user.email);
        const result = await transporter.sendMail(testMailOptions);

        console.log('✅ Test email sent successfully');
        console.log('Message ID:', result.messageId);

        res.json({
            success: true,
            message: 'Test email sent successfully',
            messageId: result.messageId,
            environment: process.env.NODE_ENV || 'development',
            sentTo: req.user.email
        });

    } catch (error) {
        console.error('❌ Test email failed:', error);
        res.status(500).json({
            success: false,
            error: error.message,
            code: error.code,
            environment: process.env.NODE_ENV || 'development'
        });
    }
});

// Delete submission endpoint
app.delete('/submissions/delete', ensureAuth, checkRole(['admin']), async (req, res) => {
    try {
        const { submissionId } = req.body;

        console.log('=== DELETE SUBMISSION REQUEST ===');
        console.log('User:', req.user.displayName);
        console.log('Submission ID:', submissionId);

        if (!submissionId) {
            return res.status(400).json({
                success: false,
                error: 'Submission ID is required'
            });
        }

        // Delete from Firebase
        const submissionRef = db.collection('submissions').doc(submissionId);
        const submissionDoc = await submissionRef.get();

        if (!submissionDoc.exists) {
            return res.status(404).json({
                success: false,
                error: 'Submission not found'
            });
        }

        // Get submission data for logging
        const submissionData = submissionDoc.data();
        console.log('Deleting submission:', {
            id: submissionId,
            name: submissionData.name,
            email: submissionData.email,
            createdAt: submissionData.createdAt
        });

        // Delete the document
        await submissionRef.delete();

        console.log('✅ Submission deleted successfully');

        res.json({
            success: true,
            message: 'Submission deleted successfully',
            deletedId: submissionId
        });

    } catch (error) {
        console.error('❌ Error deleting submission:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to delete submission: ' + error.message
        });
    }
});

// Use routes
app.use('/', indexRoutes);
app.use('/auth', authRoutes); // Removed rate limiting to fix OAuth
app.use('/admin', adminRoutes);
app.use('/submissions', submissionsRoutes);
app.use('/logo-settings', logoRoutes);
app.use('/pricing', pricingRoutes);

// Export the function for use in other modules
module.exports.generateAndSendPDF = generateAndSendPDF;

// Start the server
const port = process.env.PORT || 3000;
const server = app.listen(port, async () => {
    console.log(`Server is running on port ${port}`);

    // Verify email configuration
    await verifyEmailConfig();

    // Start performance monitoring
    performanceMonitor.startMemoryMonitoring();
    console.log('Server started successfully with performance monitoring');
});

// Graceful shutdown handlers
async function gracefulShutdown(signal) {
    console.log(`\nReceived ${signal}. Starting graceful shutdown...`);

    try {
        // Close HTTP server
        server.close(() => {
            console.log('HTTP server closed');
        });

        // Cleanup document service (closes Puppeteer browser)
        await documentService.cleanup();
        console.log('Document service cleanup completed');

        console.log('Graceful shutdown completed');
        process.exit(0);
    } catch (error) {
        console.error('Error during graceful shutdown:', error);
        process.exit(1);
    }
}

// Handle various shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('SIGUSR2', () => gracefulShutdown('SIGUSR2')); // nodemon restart

// Handle uncaught exceptions
process.on('uncaughtException', async (error) => {
    console.error('Uncaught Exception:', error);
    await gracefulShutdown('uncaughtException');
});

process.on('unhandledRejection', async (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    await gracefulShutdown('unhandledRejection');
});
