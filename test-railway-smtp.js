// Test SMTP connection for Railway deployment
const nodemailer = require('nodemailer');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

console.log('Testing Railway SMTP connection...');
console.log('Environment:', process.env.NODE_ENV || 'development');

// Railway-optimized SMTP configuration
const transporterConfig = {
    host: 'smtp-relay.brevo.com',
    port: process.env.NODE_ENV === 'production' ? 465 : 587,
    secure: process.env.NODE_ENV === 'production' ? true : false,
    auth: {
        user: process.env.BREVO_SMTP_USER,
        pass: process.env.BREVO_SMTP_PASS
    },
    connectionTimeout: 30000,
    greetingTimeout: 15000,
    socketTimeout: 30000,
    pool: false,
    maxConnections: 1,
    tls: {
        rejectUnauthorized: false,
        minVersion: 'TLSv1.2',
        maxVersion: 'TLSv1.3'
    },
    debug: true,
    logger: true
};

console.log('SMTP Configuration:', {
    host: transporterConfig.host,
    port: transporterConfig.port,
    secure: transporterConfig.secure,
    user: transporterConfig.auth.user ? 'Set' : 'Not set',
    pass: transporterConfig.auth.pass ? 'Set' : 'Not set'
});

async function testSMTP() {
    try {
        console.log('\n=== Creating transporter ===');
        const transporter = nodemailer.createTransport(transporterConfig);
        
        console.log('\n=== Verifying connection ===');
        await transporter.verify();
        console.log('✅ SMTP connection verified successfully!');
        
        console.log('\n=== Sending test email ===');
        const testMailOptions = {
            from: '"Kalkulace Test" <<EMAIL>>',
            to: '<EMAIL>',
            subject: 'Railway SMTP Test - ' + new Date().toISOString(),
            text: 'This is a test email to verify Railway SMTP configuration is working.',
            html: '<p>This is a test email to verify <strong>Railway SMTP</strong> configuration is working.</p>'
        };
        
        const result = await transporter.sendMail(testMailOptions);
        console.log('✅ Test email sent successfully!');
        console.log('Message ID:', result.messageId);
        console.log('Response:', result.response);
        
        return true;
    } catch (error) {
        console.error('❌ SMTP test failed:');
        console.error('Error type:', error.constructor.name);
        console.error('Error message:', error.message);
        console.error('Error code:', error.code);
        console.error('Error command:', error.command);
        
        if (error.stack) {
            console.error('Stack trace:', error.stack);
        }
        
        return false;
    }
}

// Run the test
testSMTP().then(success => {
    if (success) {
        console.log('\n🎉 Railway SMTP test completed successfully!');
        process.exit(0);
    } else {
        console.log('\n💥 Railway SMTP test failed!');
        process.exit(1);
    }
}).catch(error => {
    console.error('\n💥 Unexpected error during SMTP test:', error);
    process.exit(1);
});
