/* Modern CSS with variables - evgroup.cz color scheme */
:root {
    --primary-color: #1e3a8a;
    --primary-hover: #1e40af;
    --secondary-color: #3b82f6;
    --text-color: #1f2937;
    --text-light: #6b7280;
    --background: #f8fafc;
    --card-bg: #ffffff;
    --success: #10b981;
    --error: #ef4444;
    --border-radius: 8px;
    --box-shadow: 0 4px 20px rgba(30, 58, 138, 0.08);
    --transition: all 0.3s ease;
}

/* Base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background-color: var(--background);
    color: var(--text-color);
    line-height: 1.6;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.container {
    width: 100%;
    max-width: 800px;
    margin: 40px auto;
    padding: 0 20px;
    flex: 1;
}

/* Form card styling */
.form-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 30px;
    margin-bottom: 30px;
}

.form-header {
    margin-bottom: 30px;
    text-align: center;
}

.form-header h1 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 10px;
    font-size: 28px;
}

.form-header .subtitle {
    color: var(--text-light);
    font-size: 16px;
}

/* Form elements */
.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-color);
}

input[type="text"],
input[type="email"],
input[type="number"],
input[type="tel"],
input[type="date"] {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #d1d5db;
    border-radius: var(--border-radius);
    font-size: 16px;
    transition: var(--transition);
    font-family: 'Poppins', sans-serif;
}

/* Enhanced date input styling */
input[type="date"] {
    min-height: 48px;
    color: var(--text-color);
    background-color: var(--card-bg);
}

input[type="date"]::-webkit-calendar-picker-indicator {
    background-color: var(--primary-color);
    border-radius: 3px;
    padding: 3px;
    cursor: pointer;
    filter: invert(1);
    margin-left: 8px;
}

input[type="date"]::-webkit-calendar-picker-indicator:hover {
    background-color: var(--primary-hover);
    transform: scale(1.1);
}

/* Mutual exclusivity styling for OSB board and inspection walkway */
.form-table input[type="number"][value="0"] {
    background-color: #f8f9fa;
    color: #6c757d;
}

.form-table input[type="number"]:not([value="0"]):not([value=""]) {
    background-color: var(--card-bg);
    color: var(--text-color);
    border-color: var(--primary-color);
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="number"]:focus,
input[type="tel"]:focus,
input[type="date"]:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
}

/* Insulation calculator specific styles */
.form-section {
    margin-bottom: 25px;
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    padding: 12px 20px;
    margin-bottom: 0;
    text-align: center;
    border-bottom: 2px solid rgba(255, 255, 255, 0.1);
}

.section-header h2, .section-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 0;
    background-color: var(--card-bg);
}

.form-table td, .form-table th {
    border: 1px solid #e9ecef;
    padding: 12px 15px;
    vertical-align: middle;
}

.form-table th {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    text-align: center;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.form-table tr:nth-child(even) {
    background-color: #f8f9fa;
}

.form-table tr:hover {
    background-color: #e3f2fd;
    transition: var(--transition);
}

.form-table input[type="text"],
.form-table input[type="email"],
.form-table input[type="tel"],
.form-table input[type="number"],
.form-table input[type="date"] {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-size: 14px;
    transition: var(--transition);
    font-family: 'Poppins', sans-serif;
}

.form-table input[type="text"]:focus,
.form-table input[type="email"]:focus,
.form-table input[type="tel"]:focus,
.form-table input[type="number"]:focus,
.form-table input[type="date"]:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
}

/* Special styling for date inputs to make them more prominent */
.form-table input[type="date"] {
    padding: 12px 15px;
    font-size: 16px;
    min-height: 48px;
    color: var(--text-color);
    background-color: var(--card-bg);
}

.form-table input[type="date"]::-webkit-calendar-picker-indicator {
    background-color: var(--primary-color);
    border-radius: 3px;
    padding: 2px;
    cursor: pointer;
    filter: invert(1);
}

.form-table input[type="date"]::-webkit-calendar-picker-indicator:hover {
    background-color: var(--primary-hover);
}

.form-table input[type="checkbox"],
.form-table input[type="radio"] {
    width: 20px;
    height: 20px;
    accent-color: var(--primary-color);
    cursor: pointer;
}

.label-cell {
    width: 120px;
    font-weight: 500;
    color: var(--text-color);
}

.calculation-note {
    background: linear-gradient(135deg, #fff3e0, #ffe0b2);
    border: 1px solid #ffb74d;
    border-left: 4px solid #ff9800;
    padding: 15px 20px;
    border-radius: var(--border-radius);
    font-size: 14px;
    margin: 20px 0;
    box-shadow: 0 2px 8px rgba(255, 152, 0, 0.1);
}

.calculation-note p {
    margin: 5px 0;
    font-weight: 500;
    color: #e65100;
}

.note {
    font-style: italic;
    font-size: 12px;
    color: var(--text-light);
    margin-top: 8px;
    padding: 8px 12px;
    background-color: rgba(67, 97, 238, 0.05);
    border-radius: var(--border-radius);
    border-left: 3px solid var(--primary-color);
}

/* Section headers without tables */
.form-section h2 {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    padding: 15px 20px;
    margin: 0 0 25px 0;
    text-align: center;
    border-radius: var(--border-radius);
    font-size: 18px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    box-shadow: 0 4px 12px rgba(67, 97, 238, 0.2);
}

/* Summary section special styling */
.form-section:has(.form-table th:contains("Shrnutí")) {
    border: 2px solid var(--primary-color);
    box-shadow: 0 4px 20px rgba(30, 58, 138, 0.15);
}

/* Responsive improvements */
@media (max-width: 768px) {
    .form-table {
        font-size: 14px;
    }

    .form-table td, .form-table th {
        padding: 8px 10px;
    }

    .section-header {
        padding: 10px 15px;
    }

    .section-header h2, .section-header h3 {
        font-size: 14px;
    }
}

/* Button styling */
.submit-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    width: 100%;
    padding: 14px 20px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    font-family: 'Poppins', sans-serif;
}

.submit-btn:hover {
    background-color: var(--primary-hover);
    transform: translateY(-2px);
}

.submit-btn:active {
    transform: translateY(0);
}

/* Response message */
.response-message {
    margin-top: 20px;
    padding: 15px;
    border-radius: var(--border-radius);
    font-weight: 500;
    text-align: center;
    display: none;
}

.response-message.success {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success);
    border: 1px solid rgba(16, 185, 129, 0.3);
    display: block;
}

.response-message.error {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--error);
    border: 1px solid rgba(239, 68, 68, 0.3);
    display: block;
}

/* Authentication UI */
.auth-status {
    margin-bottom: 25px;
    padding: 15px;
    border-radius: var(--border-radius);
    background-color: var(--background);
    border: 1px solid #e5e7eb;
}

.auth-loading {
    text-align: center;
    color: var(--text-light);
}

.auth-loading i {
    margin-right: 8px;
    color: var(--primary-color);
}

.not-logged-in {
    text-align: center;
}

.not-logged-in p {
    margin-bottom: 15px;
    color: var(--text-light);
}

.login-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 10px 20px;
    background-color: var(--primary-color);
    color: white;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.login-btn:hover {
    background-color: var(--primary-hover);
    transform: translateY(-2px);
}

.user-info-compact {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.avatar-small {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.dashboard-link, .logout-link {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-size: 14px;
    transition: var(--transition);
    margin-left: auto;
}

.dashboard-link {
    background-color: var(--primary-color);
    color: white;
}

.logout-link {
    background-color: #f8fafc;
    color: var(--text-color);
    border: 1px solid #d1d5db;
}

.dashboard-link:hover, .logout-link:hover {
    transform: translateY(-2px);
}

/* Back link styling */
.back-link {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.back-link:hover {
    color: var(--primary-hover);
}

@media (max-width: 600px) {
    .user-info-compact {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .dashboard-link, .logout-link {
        margin-left: 0;
    }
}

/* Footer */
.footer {
    text-align: center;
    padding: 20px 0;
    color: var(--text-light);
    font-size: 14px;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        margin: 20px auto;
    }

    .form-card {
        padding: 20px;
    }

    .form-row {
        flex-direction: column;
        gap: 0;
    }

    .form-row .form-group {
        margin-bottom: 20px;
    }

    .form-header h1 {
        font-size: 24px;
    }
}