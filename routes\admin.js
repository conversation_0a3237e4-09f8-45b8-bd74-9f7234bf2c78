const express = require('express');
const router = express.Router();
const { ensureAuth, checkRole } = require('../middleware/auth');
const { admin, db } = require('../firebase-admin');

// Helper function to fix Google profile image URLs
function fixGoogleProfileImageUrl(profileImage) {
  if (!profileImage || !profileImage.includes('googleusercontent.com')) {
    return profileImage;
  }

  // Remove size parameter and add a reliable size
  const baseUrl = profileImage.split('=')[0];
  return baseUrl + '=s96-c';
}

// Get all users (API endpoint for AJAX)
router.get('/users', ensureAuth, checkRole(['admin']), async (req, res) => {
  try {
    const usersSnapshot = await db.collection('users').get();
    const users = [];

    usersSnapshot.forEach(doc => {
      const userData = doc.data();
      // Fix profile image URL on the fly
      if (userData.profileImage) {
        userData.profileImage = fixGoogleProfileImageUrl(userData.profileImage);
      }

      users.push({
        id: doc.id,
        ...userData
      });
    });

    res.json(users);
  } catch (err) {
    console.error('Error fetching users:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// Update user role
router.post('/update-role', ensureAuth, checkRole(['admin']), async (req, res) => {
  try {
    const { userId, role } = req.body;

    // Don't allow changing your own role (prevents locking yourself out)
    if (userId === req.user.id) {
      return res.status(400).json({ message: 'Cannot change your own role' });
    }

    // Get user document reference
    const userRef = db.collection('users').doc(userId);
    const userDoc = await userRef.get();

    console.log('Updating user role:', { userId, role });

    if (!userDoc.exists) {
      console.log('User not found with ID:', userId);
      return res.status(404).json({ message: 'User not found' });
    }

    // Update the user's role in Firestore
    await userRef.update({ role });
    console.log('User role updated successfully');
    res.json({ success: true });
  } catch (err) {
    console.error('Error updating user role:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
