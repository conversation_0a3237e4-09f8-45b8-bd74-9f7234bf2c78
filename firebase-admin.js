const admin = require('firebase-admin');

// Use environment variable in production, fallback to local file in development
let serviceAccount;
if (process.env.FIREBASE_ADMIN_KEY) {
  serviceAccount = JSON.parse(process.env.FIREBASE_ADMIN_KEY);
} else {
  serviceAccount = require('./pavelapplikace-firebase-adminsdk-fbsvc-74f5788735.json');
}

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

// The bucket name will be determined from the project ID in the service account

const db = admin.firestore();

// Configure Firestore to ignore undefined values
db.settings({
  ignoreUndefinedProperties: true
});

module.exports = { admin, db };
