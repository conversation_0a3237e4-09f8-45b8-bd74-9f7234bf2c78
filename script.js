const form = document.getElementById('contactForm');
const responseMessage = document.getElementById('responseMessage');
const authStatus = document.getElementById('auth-status');
const authLoading = document.getElementById('auth-loading');
const notLoggedIn = document.querySelector('.not-logged-in');
const loggedIn = document.querySelector('.logged-in');
const userAvatar = document.getElementById('user-avatar');
const userName = document.getElementById('user-name');

// Global pricing configuration
let pricingConfig = {
    facade: {
        polystyrene: 2320,
        mineralWool: 2680,
        revealCost: 215,
        subsidy: 1300
    },
    roof: {
        withoutCladding: 1650,
        withCladding: 2750,
        subsidy: 1300
    },
    attic: {
        baseCost: 775,
        withFloorRemoval: 1025,
        osbBoardCost: 1195,
        walkwayCost: 850,
        subsidy: 500
    },
    wall: {
        baseCost: 1250,
        subsidy: 500
    },
    floor: {
        baseCost: 2200,
        subsidy: 1700
    },
    windows: {
        baseCost: 6700,
        subsidy: 4900
    },
    shading: {
        areaCost: 2900,
        windowWidthCost: 3000,
        subsidy: 1500
    },
    heatPump: {
        power8kW: 225000,
        power12kW: 260000,
        power16kW: 285000,
        power22kW: 310000,
        subsidyHeatingOnly: 75000,
        subsidyHeatingWater: 90000
    },
    photovoltaic: {
        slopedRoof: 72300,
        flatRoof: 78200,
        newBoiler: 12100,
        subsidy: 35000
    },
    heatRecovery: {
        baseCost: 210000,
        subsidy: 90000
    },
    rainwater: {
        baseCost: 45000,
        costPerM3: 9000,
        baseSubsidy: 20000,
        subsidyPerM3: 3000
    },
    fveBattery: {
        system5_4kWp: 260000,  // 5,4 kWp + 6,2 kWh
        system7_2kWp: 300000,  // 7,2 kWp + 9,3 kWh
        system9_9kWp: 320000,  // 9,9 kWp + 11,6 kWh
        system14_4kWp: 410000, // 14,4 kWp + 15,5 kWh
        wallboxCost: 15000,    // Cost per wallbox
        baseSubsidy: 100000,   // Base subsidy for FVE system
        gridSubsidy: 40000     // Additional subsidy for grid connection
    },
    bonuses: {
        regionalBonusPercent: 5,
        childFullCare: 50000,
        childPartialCare: 25000,
        combinationInsulationFVE: 50000,      // Zateplení + FVE
        combinationInsulationHeatSource: 50000, // Zateplení + zdroj tepla
        basicSupport: 50000
    }
};

// Load pricing configuration from server
async function loadPricingConfiguration() {
    try {
        const response = await fetch('/pricing/get');
        if (response.ok) {
            const serverPricing = await response.json();
            // Merge server pricing with defaults
            pricingConfig = { ...pricingConfig, ...serverPricing };
            console.log('Pricing configuration loaded:', pricingConfig);
        } else {
            console.warn('Failed to load pricing configuration, using defaults');
        }
    } catch (error) {
        console.warn('Error loading pricing configuration, using defaults:', error);
    }
}



// Initialize the application
async function initializeApp() {
    await loadPricingConfiguration();
    await checkAuthStatus();
}

// Check authentication status when page loads
async function checkAuthStatus() {
    try {
        const response = await fetch('/auth/status');
        const data = await response.json();

        if (data.authenticated && data.user) {
            // User is logged in
            showLoggedInState(data.user);
        } else {
            // User is not logged in
            showLoggedOutState();
        }
    } catch (error) {
        console.error('Error checking auth status:', error);
        showLoggedOutState();
    }
}

// Helper function to get reliable profile image URL
function getReliableProfileImage(profileImage, displayName) {
    if (!profileImage || profileImage.trim() === '') {
        return getInitialsAvatar(displayName || 'User');
    }

    // Fix Google profile image URLs
    if (profileImage.includes('googleusercontent.com')) {
        const baseUrl = profileImage.split('=')[0];
        return baseUrl + '=s96-c';
    }

    return profileImage;
}

// Helper function to generate initials avatar
function getInitialsAvatar(name) {
    const initials = (name || 'User').split(' ').map(n => n[0]).join('').substring(0, 2).toUpperCase();
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&background=007bff&color=fff&size=96&bold=true`;
}

// Show logged in state
function showLoggedInState(user) {
    authLoading.style.display = 'none';
    notLoggedIn.style.display = 'none';
    loggedIn.style.display = 'block';
    form.style.display = 'block';

    // Set user info with reliable profile image
    const reliableImageUrl = getReliableProfileImage(user.profileImage, user.displayName || user.name);
    const fallbackUrl = getInitialsAvatar(user.displayName || user.name || 'User');

    userAvatar.src = reliableImageUrl;
    userAvatar.onerror = function() {
        this.src = fallbackUrl;
        this.onerror = null; // Prevent infinite loop
    };

    userName.textContent = user.name || user.displayName || 'User';

    // Pre-fill form with user data if available
    if (user.email) {
        document.getElementById('email').value = user.email;
    }
    if (user.displayName) {
        document.getElementById('name').value = user.displayName;
    }
}

// Show logged out state
function showLoggedOutState() {
    authLoading.style.display = 'none';
    notLoggedIn.style.display = 'block';
    loggedIn.style.display = 'none';
    form.style.display = 'none';
}

// Helper function to show response messages
function showResponse(message, isSuccess) {
    // Remove any existing classes
    responseMessage.classList.remove('success', 'error');

    // Add appropriate class
    responseMessage.classList.add(isSuccess ? 'success' : 'error');

    // Set message text
    responseMessage.textContent = message;

    // Make sure it's displayed
    responseMessage.style.display = 'block';

    // Scroll to response if needed
    responseMessage.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
}

// Handle form submission
form.addEventListener('submit', async (event) => {
    event.preventDefault(); // Prevent default form submission

    // Get personal information
    const name = document.getElementById('name').value;
    const email = document.getElementById('email').value;
    const address = document.getElementById('address').value;
    const phone = document.getElementById('phone').value;
    const dateOfBirth = document.getElementById('dateOfBirth').value;
    const realizationAddress = document.getElementById('realizationAddress').value;

    // Get facade insulation values
    const facadeAreaSubsidized = parseFloat(document.getElementById('facadeAreaSubsidized').value) || 0;
    const facadeAreaNonSubsidized = parseFloat(document.getElementById('facadeAreaNonSubsidized').value) || 0;
    const totalRevealLength = parseFloat(document.getElementById('totalRevealLength').value) || 0;
    const materialPolystyrene = document.getElementById('materialPolystyrene').checked;
    const materialMineralWool = document.getElementById('materialMineralWool').checked;

    // Get roof insulation values
    const roofAreaSubsidized = parseFloat(document.getElementById('roofAreaSubsidized').value) || 0;
    const roofAreaNonSubsidized = parseFloat(document.getElementById('roofAreaNonSubsidized').value) || 0;
    const additionalRoofWork = document.getElementById('additionalRoofWork').checked;

    // Get attic ceiling insulation values
    const atticAreaSubsidized = parseFloat(document.getElementById('atticAreaSubsidized').value) || 0;
    const atticAreaNonSubsidized = parseFloat(document.getElementById('atticAreaNonSubsidized').value) || 0;
    const atticFloorRemoval = document.getElementById('atticFloorRemoval').checked;
    const osbBoardArea = parseFloat(document.getElementById('osbBoardArea').value) || 0;
    const inspectionWalkwayLength = parseFloat(document.getElementById('inspectionWalkwayLength').value) || 0;

    // Get wall insulation values
    const wallAreaSubsidized = parseFloat(document.getElementById('wallAreaSubsidized').value) || 0;
    const wallAreaNonSubsidized = parseFloat(document.getElementById('wallAreaNonSubsidized').value) || 0;
    const wallSdkCladding = document.getElementById('wallSdkCladding').checked;
    const wallPlastering = document.getElementById('wallPlastering').checked;

    // Get floor insulation values
    const floorAreaSubsidized = parseFloat(document.getElementById('floorAreaSubsidized').value) || 0;
    const floorAreaNonSubsidized = parseFloat(document.getElementById('floorAreaNonSubsidized').value) || 0;

    // Get windows and doors values
    const windowsAreaSubsidized = parseFloat(document.getElementById('windowsAreaSubsidized').value) || 0;
    const windowsAreaNonSubsidized = parseFloat(document.getElementById('windowsAreaNonSubsidized').value) || 0;

    // Get shading technology values
    const shadingAreaSubsidized = parseFloat(document.getElementById('shadingAreaSubsidized').value) || 0;
    const shadingAreaNonSubsidized = parseFloat(document.getElementById('shadingAreaNonSubsidized').value) || 0;
    const totalWindowWidth = parseFloat(document.getElementById('totalWindowWidth').value) || 0;

    // Get heat pump values
    const heatPump8kW = document.getElementById('heatPump8kW').checked;
    const heatPump12kW = document.getElementById('heatPump12kW').checked;
    const heatPump16kW = document.getElementById('heatPump16kW').checked;
    const heatPump22kW = document.getElementById('heatPump22kW').checked;
    const heatPumpHeatingOnly = document.getElementById('heatPumpHeatingOnly').checked;
    const heatPumpHeatingWater = document.getElementById('heatPumpHeatingWater').checked;

    // Get photovoltaic water heating values
    const roofTypeSloped = document.getElementById('roofTypeSloped').checked;
    const roofTypeFlat = document.getElementById('roofTypeFlat').checked;
    const newBoilerYes = document.getElementById('newBoilerYes').checked;
    const newBoilerNo = document.getElementById('newBoilerNo').checked;

    // Get heat recovery values
    const heatRecoveryYes = document.getElementById('heatRecoveryYes').checked;
    const heatRecoveryNo = document.getElementById('heatRecoveryNo').checked;

    // Get rainwater values
    const rainwaterTankSize = parseFloat(document.getElementById('rainwaterTankSize').value) || 0;

    // Get bonus values
    const regionalBonus = document.getElementById('regionalBonus').checked;
    const childrenFullCare = parseInt(document.getElementById('childrenFullCare').value) || 0;
    const childrenPartialCare = parseInt(document.getElementById('childrenPartialCare').value) || 0;
    const combinationBonusInsulationFVE = document.getElementById('combinationBonusInsulationFVE').checked;
    const combinationBonusInsulationHeatSource = document.getElementById('combinationBonusInsulationHeatSource').checked;

    // Validate form
    if (!name || !email) {
        showResponse('Prosím vyplňte jméno a email.', false);
        return;
    }

    // Material selection is now handled by radio buttons, so this validation is no longer needed
    // But we'll keep it for backward compatibility
    if (materialPolystyrene && materialMineralWool) {
        showResponse('Vyberte pouze jeden typ materiálu pro zateplení fasády.', false);
        return;
    }

    // Note: OSB board area and inspection walkway are now mutually exclusive through automatic UX

    // Wall insulation options are now handled by radio buttons, so this validation is no longer needed
    // But we'll keep it for backward compatibility
    if (wallSdkCladding && wallPlastering) {
        showResponse('Nelze zvolit zaklopení SDK a zároveň zapravení do štuku. Prosím vyberte pouze jednu možnost.', false);
        return;
    }

    // Count selected heat pump power options
    const heatPumpPowerCount = [heatPump8kW, heatPump12kW, heatPump16kW, heatPump22kW].filter(Boolean).length;

    // Validate heat pump power selection
    if (heatPumpPowerCount > 1) {
        showResponse('Vyberte pouze jeden výkon tepelného čerpadla.', false);
        return;
    }

    // Count selected heat pump usage options
    const heatPumpUsageCount = [heatPumpHeatingOnly, heatPumpHeatingWater].filter(Boolean).length;

    // Validate heat pump usage selection
    if (heatPumpUsageCount > 1) {
        showResponse('Vyberte pouze jeden způsob využití tepelného čerpadla.', false);
        return;
    }

    // Count selected roof type options
    const roofTypeCount = [roofTypeSloped, roofTypeFlat].filter(Boolean).length;

    // Validate roof type selection
    if (roofTypeCount > 1) {
        showResponse('Vyberte pouze jeden typ střechy.', false);
        return;
    }

    // Count selected new boiler options
    const newBoilerCount = [newBoilerYes, newBoilerNo].filter(Boolean).length;

    // Validate new boiler selection
    if (newBoilerCount > 1) {
        showResponse('Vyberte pouze jednu možnost pro nový bojler.', false);
        return;
    }

    // Calculate costs
    let cena = 0; // Main variable to accumulate total cost
    let facadeCost = 0;
    let roofCost = 0;
    let atticCost = 0;
    let wallCost = 0;
    let floorCost = 0;
    let windowsCost = 0;
    let shadingCost = 0;
    let heatPumpCost = 0;
    let photovoltaicCost = 0;
    let fveBatteryCost = 0;
    let heatRecoveryCost = 0;
    let rainwaterCost = 0;
    let facadeSubsidy = 0;
    let roofSubsidy = 0;
    let atticSubsidy = 0;
    let wallSubsidy = 0;
    let floorSubsidy = 0;
    let windowsSubsidy = 0;
    let shadingSubsidy = 0;
    let heatPumpSubsidy = 0;
    let photovoltaicSubsidy = 0;
    let fveBatterySubsidy = 0;
    let heatRecoverySubsidy = 0;
    let rainwaterSubsidy = 0;
    let regionalBonusAmount = 0;
    let familyBonusAmount = 0;
    let combinationBonusAmount = 0;
    let basicSupportAmount = 50000; // Automatic basic support
    let totalCost = 0;
    let totalSubsidy = 0;
    let totalBonuses = 0;
    let finalCost = 0;
    let totalCostWithVAT = 0;

    // Facade calculations
    const totalFacadeArea = facadeAreaSubsidized + facadeAreaNonSubsidized;
    const revealCost = totalRevealLength * pricingConfig.facade.revealCost;

    // Calculate facade cost based on selected material
    if (materialPolystyrene) {
        // 1. Polystyren: (dotovaná + nedotovaná plocha) x polystyrene price + délka špalet x reveal cost
        facadeCost = totalFacadeArea * pricingConfig.facade.polystyrene + revealCost;
        // Add to cena
        cena += (totalFacadeArea * pricingConfig.facade.polystyrene) + (totalRevealLength * pricingConfig.facade.revealCost);
    } else if (materialMineralWool) {
        // 2. Minerální vata: (dotovaná + nedotovaná plocha) x mineral wool price + délka špalet x reveal cost
        facadeCost = totalFacadeArea * pricingConfig.facade.mineralWool + revealCost;
        // Add to cena
        cena += (totalFacadeArea * pricingConfig.facade.mineralWool) + (totalRevealLength * pricingConfig.facade.revealCost);
    }

    facadeSubsidy = facadeAreaSubsidized * pricingConfig.facade.subsidy;

    // Roof calculations
    const totalRoofArea = roofAreaSubsidized + roofAreaNonSubsidized;

    // Calculate roof cost based on additional work
    if (additionalRoofWork) {
        // 2. Se záklopem: (dotovaná + nedotovaná plocha) x with cladding price
        roofCost = totalRoofArea * pricingConfig.roof.withCladding;
        // Add to cena
        cena += totalRoofArea * pricingConfig.roof.withCladding;
    } else {
        // 1. Bez záklopu: (dotovaná + nedotovaná plocha) x without cladding price
        roofCost = totalRoofArea * pricingConfig.roof.withoutCladding;
        // Add to cena
        cena += totalRoofArea * pricingConfig.roof.withoutCladding;
    }

    // Výše dotace = dotovaná plocha x roof subsidy
    roofSubsidy = roofAreaSubsidized * pricingConfig.roof.subsidy;

    // Attic ceiling calculations
    const totalAtticArea = atticAreaSubsidized + atticAreaNonSubsidized;
    let atticBaseCost = pricingConfig.attic.baseCost;

    // Calculate attic cost based on floor removal
    if (atticFloorRemoval) {
        // Pokud klient zaškrtne demontáž stávající podlahy je potřeba navýšit částku za realizaci
        atticBaseCost = pricingConfig.attic.withFloorRemoval;
    }

    // (Dotovaná + nedotovaná plocha) x base cost
    atticCost = totalAtticArea * atticBaseCost;

    // Add to cena - (Dotovaná + nedotovaná plocha) x base cost
    cena += totalAtticArea * atticBaseCost;

    // Add OSB board costs - Plocha záklopu cena
    if (osbBoardArea > 0) {
        atticCost += osbBoardArea * pricingConfig.attic.osbBoardCost;
        cena += osbBoardArea * pricingConfig.attic.osbBoardCost;
    }

    // Add inspection walkway costs - Revizní lávka
    if (inspectionWalkwayLength > 0) {
        atticCost += inspectionWalkwayLength * pricingConfig.attic.walkwayCost;
        cena += inspectionWalkwayLength * pricingConfig.attic.walkwayCost;
    }

    // Výše dotace = dotovaná plocha x attic subsidy
    atticSubsidy = atticAreaSubsidized * pricingConfig.attic.subsidy;

    // Wall insulation calculations
    const totalWallArea = wallAreaSubsidized + wallAreaNonSubsidized;
    let wallBaseCost = pricingConfig.wall.baseCost;

    // (Dotovaná + nedotovaná plocha) x wall base cost
    wallCost = totalWallArea * wallBaseCost;

    // Add to cena
    cena += totalWallArea * wallBaseCost;

    // Výše dotace = dotovaná plocha x wall subsidy
    wallSubsidy = wallAreaSubsidized * pricingConfig.wall.subsidy;

    // Floor insulation calculations
    const totalFloorArea = floorAreaSubsidized + floorAreaNonSubsidized;

    // (Dotovaná + nedotovaná plocha) x floor base cost
    floorCost = totalFloorArea * pricingConfig.floor.baseCost;

    // Add to cena
    cena += totalFloorArea * pricingConfig.floor.baseCost;

    // Výše dotace = dotovaná plocha x floor subsidy
    floorSubsidy = floorAreaSubsidized * pricingConfig.floor.subsidy;

    // Windows and doors calculations
    const totalWindowsArea = windowsAreaSubsidized + windowsAreaNonSubsidized;

    // (Dotovaná + nedotovaná plocha) x windows base cost
    windowsCost = totalWindowsArea * pricingConfig.windows.baseCost;

    // Add to cena
    cena += totalWindowsArea * pricingConfig.windows.baseCost;

    // Výše dotace = dotovaná plocha x windows subsidy
    windowsSubsidy = windowsAreaSubsidized * pricingConfig.windows.subsidy;

    // Shading technology calculations
    const totalShadingArea = shadingAreaSubsidized + shadingAreaNonSubsidized;

    // (Dotovaná + nedotovaná plocha) x area cost + celková šířka oken x window width cost
    shadingCost = (totalShadingArea * pricingConfig.shading.areaCost) + (totalWindowWidth * pricingConfig.shading.windowWidthCost);

    // Add to cena
    cena += (totalShadingArea * pricingConfig.shading.areaCost) + (totalWindowWidth * pricingConfig.shading.windowWidthCost);

    // Výše dotace = dotovaná plocha x shading subsidy
    shadingSubsidy = shadingAreaSubsidized * pricingConfig.shading.subsidy;

    // Total subsidy calculation for Area A
    totalSubsidy = facadeSubsidy + roofSubsidy + atticSubsidy + wallSubsidy + floorSubsidy + windowsSubsidy + shadingSubsidy;

    // Heat pump calculations
    if (heatPump8kW) {
        // 8 kW
        heatPumpCost = pricingConfig.heatPump.power8kW;
        cena += pricingConfig.heatPump.power8kW;
    } else if (heatPump12kW) {
        // 12 kW
        heatPumpCost = pricingConfig.heatPump.power12kW;
        cena += pricingConfig.heatPump.power12kW;
    } else if (heatPump16kW) {
        // 16 kW
        heatPumpCost = pricingConfig.heatPump.power16kW;
        cena += pricingConfig.heatPump.power16kW;
    } else if (heatPump22kW) {
        // 22 kW
        heatPumpCost = pricingConfig.heatPump.power22kW;
        cena += pricingConfig.heatPump.power22kW;
    }

    if (heatPumpHeatingOnly) {
        // Pouze vytápění
        heatPumpSubsidy = pricingConfig.heatPump.subsidyHeatingOnly;
    } else if (heatPumpHeatingWater) {
        // Vytápění + ohřev vody
        heatPumpSubsidy = pricingConfig.heatPump.subsidyHeatingWater;
    }

    // Photovoltaic water heating calculations
    photovoltaicCost = 0;

    if (roofTypeSloped) {
        // šikmá střecha
        photovoltaicCost += pricingConfig.photovoltaic.slopedRoof;
        cena += pricingConfig.photovoltaic.slopedRoof;
    } else if (roofTypeFlat) {
        // rovná střecha
        photovoltaicCost += pricingConfig.photovoltaic.flatRoof;
        cena += pricingConfig.photovoltaic.flatRoof;
    }

    if (newBoilerYes) {
        // nový bojler
        photovoltaicCost += pricingConfig.photovoltaic.newBoiler;
        cena += pricingConfig.photovoltaic.newBoiler;
    }

    if (roofTypeSloped || roofTypeFlat) {
        // Výše dotace
        photovoltaicSubsidy = pricingConfig.photovoltaic.subsidy;
    }

    // Get FVE battery system values
    const fveBattery5_4 = document.getElementById('fveBattery5_4').checked;
    const fveBattery7_2 = document.getElementById('fveBattery7_2').checked;
    const fveBattery9_9 = document.getElementById('fveBattery9_9').checked;
    const fveBattery14_4 = document.getElementById('fveBattery14_4').checked;
    const wallboxCount = parseInt(document.getElementById('wallboxCount').value) || 0;
    const gridConnectionYes = document.getElementById('gridConnectionYes').checked;

    // FVE battery system calculations
    fveBatteryCost = 0;
    fveBatterySubsidy = 0;

    if (fveBattery5_4) {
        // 5,4 kWp + 6,2 kWh
        fveBatteryCost = pricingConfig.fveBattery.system5_4kWp;
        cena += pricingConfig.fveBattery.system5_4kWp;
    } else if (fveBattery7_2) {
        // 7,2 kWp + 9,3 kWh
        fveBatteryCost = pricingConfig.fveBattery.system7_2kWp;
        cena += pricingConfig.fveBattery.system7_2kWp;
    } else if (fveBattery9_9) {
        // 9,9 kWp + 11,6 kWh
        fveBatteryCost = pricingConfig.fveBattery.system9_9kWp;
        cena += pricingConfig.fveBattery.system9_9kWp;
    } else if (fveBattery14_4) {
        // 14,4 kWp + 15,5 kWh
        fveBatteryCost = pricingConfig.fveBattery.system14_4kWp;
        cena += pricingConfig.fveBattery.system14_4kWp;
    }

    // Add wallbox costs
    if (wallboxCount > 0) {
        const wallboxCost = wallboxCount * pricingConfig.fveBattery.wallboxCost;
        fveBatteryCost += wallboxCost;
        cena += wallboxCost;
    }

    // Calculate FVE battery subsidies
    if (fveBattery5_4 || fveBattery7_2 || fveBattery9_9 || fveBattery14_4) {
        // Base subsidy for FVE system
        fveBatterySubsidy = pricingConfig.fveBattery.baseSubsidy;

        // Additional subsidy for grid connection
        if (gridConnectionYes) {
            fveBatterySubsidy += pricingConfig.fveBattery.gridSubsidy;
        }
    }

    // Heat recovery calculations
    if (heatRecoveryYes) {
        heatRecoveryCost = pricingConfig.heatRecovery.baseCost;
        heatRecoverySubsidy = pricingConfig.heatRecovery.subsidy;
        cena += heatRecoveryCost;
    }

    // Rainwater calculations
    if (rainwaterTankSize > 0) {
        rainwaterCost = pricingConfig.rainwater.baseCost + (rainwaterTankSize * pricingConfig.rainwater.costPerM3);
        rainwaterSubsidy = pricingConfig.rainwater.baseSubsidy + (rainwaterTankSize * pricingConfig.rainwater.subsidyPerM3);
        cena += rainwaterCost;
    }

    // Apply maximum subsidy limit of 1,000,000 CZK for Area A
    let areaASubsidy = facadeSubsidy + roofSubsidy + atticSubsidy + wallSubsidy + floorSubsidy + windowsSubsidy + shadingSubsidy;
    if (areaASubsidy > 1000000) {
        // Calculate the reduction factor
        const reductionFactor = 1000000 / areaASubsidy;

        // Apply the reduction to each subsidy in Area A
        facadeSubsidy *= reductionFactor;
        roofSubsidy *= reductionFactor;
        atticSubsidy *= reductionFactor;
        wallSubsidy *= reductionFactor;
        floorSubsidy *= reductionFactor;
        windowsSubsidy *= reductionFactor;
        shadingSubsidy *= reductionFactor;

        areaASubsidy = 1000000;
    }

    // Calculate base subsidies (Area A + Area C)
    const baseSubsidies = areaASubsidy + heatPumpSubsidy + photovoltaicSubsidy + fveBatterySubsidy + heatRecoverySubsidy + rainwaterSubsidy;

    // Set total cost from cena variable
    totalCost = cena;

    // Calculate total cost with VAT
    totalCostWithVAT = totalCost * 1.12;

    // Calculate family bonus
    familyBonusAmount = (childrenFullCare * pricingConfig.bonuses.childFullCare) + (childrenPartialCare * pricingConfig.bonuses.childPartialCare);

    // Calculate combination bonus - work independently when checked
    if (combinationBonusInsulationFVE) {
        combinationBonusAmount += pricingConfig.bonuses.combinationInsulationFVE;
    }

    if (combinationBonusInsulationHeatSource) {
        combinationBonusAmount += pricingConfig.bonuses.combinationInsulationHeatSource;
    }

    // Calculate regional bonus (percentage of base subsidies only)
    if (regionalBonus) {
        regionalBonusAmount = baseSubsidies * (pricingConfig.bonuses.regionalBonusPercent / 100);
    }

    // Calculate total bonuses
    basicSupportAmount = pricingConfig.bonuses.basicSupport;
    totalBonuses = regionalBonusAmount + familyBonusAmount + combinationBonusAmount + basicSupportAmount;

    // Calculate total subsidy including all bonuses
    totalSubsidy = baseSubsidies + totalBonuses;

    // Calculate final cost
    finalCost = totalCostWithVAT - totalSubsidy;

    // Update summary fields in the form
    document.getElementById('totalCostWithoutVAT').textContent = totalCost.toLocaleString() + ' Kč';
    document.getElementById('totalCostWithVAT').textContent = totalCostWithVAT.toLocaleString() + ' Kč';
    document.getElementById('totalSubsidyWithBonuses').textContent = totalSubsidy.toLocaleString() + ' Kč';
    document.getElementById('finalDifference').textContent = finalCost.toLocaleString() + ' Kč';

    // Show loading state
    responseMessage.textContent = 'Zpracování požadavku...';
    responseMessage.style.display = 'block';
    responseMessage.classList.remove('success', 'error');

    // Add loading animation to button
    const submitBtn = document.querySelector('.submit-btn');
    const originalBtnText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Zpracování...';
    submitBtn.disabled = true;

    try {
        // Prepare data for submission
        const formData = {
            name,
            email,
            address,
            phone,
            dateOfBirth,
            realizationAddress,
            // Facade data
            facadeAreaSubsidized,
            facadeAreaNonSubsidized,
            totalRevealLength,
            materialPolystyrene,
            materialMineralWool,
            // Roof data
            roofAreaSubsidized,
            roofAreaNonSubsidized,
            additionalRoofWork,
            // Attic ceiling data
            atticAreaSubsidized,
            atticAreaNonSubsidized,
            atticFloorRemoval,
            osbBoardArea,
            inspectionWalkwayLength,
            // Wall data
            wallAreaSubsidized,
            wallAreaNonSubsidized,
            wallSdkCladding,
            wallPlastering,
            // Floor data
            floorAreaSubsidized,
            floorAreaNonSubsidized,
            // Windows data
            windowsAreaSubsidized,
            windowsAreaNonSubsidized,
            // Shading data
            shadingAreaSubsidized,
            shadingAreaNonSubsidized,
            totalWindowWidth,
            // Heat pump data
            heatPump8kW,
            heatPump12kW,
            heatPump16kW,
            heatPump22kW,
            heatPumpHeatingOnly,
            heatPumpHeatingWater,
            // Photovoltaic water heating data
            roofTypeSloped,
            roofTypeFlat,
            newBoilerYes,
            newBoilerNo,
            // FVE battery system data
            fveBattery5_4,
            fveBattery7_2,
            fveBattery9_9,
            fveBattery14_4,
            wallboxCount,
            gridConnectionYes,
            // Heat recovery data
            heatRecoveryYes,
            heatRecoveryNo,
            // Rainwater data
            rainwaterTankSize,
            // Bonus data
            regionalBonus,
            childrenFullCare,
            childrenPartialCare,
            combinationBonusInsulationFVE,
            combinationBonusInsulationHeatSource,
            // Cost calculations
            facadeCost,
            roofCost,
            atticCost,
            wallCost,
            floorCost,
            windowsCost,
            shadingCost,
            heatPumpCost,
            photovoltaicCost,
            fveBatteryCost,
            heatRecoveryCost,
            rainwaterCost,
            facadeSubsidy,
            roofSubsidy,
            atticSubsidy,
            wallSubsidy,
            floorSubsidy,
            windowsSubsidy,
            shadingSubsidy,
            heatPumpSubsidy,
            photovoltaicSubsidy,
            fveBatterySubsidy,
            heatRecoverySubsidy,
            rainwaterSubsidy,
            // Bonus calculations
            regionalBonusAmount,
            familyBonusAmount,
            combinationBonusAmount,
            basicSupportAmount,
            totalBonuses,
            // Summary calculations
            totalCost,
            totalCostWithVAT,
            totalSubsidy,
            finalCost
        };

        const response = await fetch('/submissions/submit-form', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData),
        });

        if (response.status === 401) {
            // User is not authenticated
            showResponse('Pro odeslání formuláře se musíte přihlásit.', false);
            setTimeout(() => {
                window.location.href = '/login';
            }, 2000);
            return;
        }

        const result = await response.json();

        if (response.status === 429) {
            // Rate limit exceeded
            showResponse(`${result.message}`, false);

            // Start a countdown timer
            const submitBtn = document.querySelector('.submit-btn');
            submitBtn.disabled = true;

            let timeRemaining = result.timeRemaining;
            const countdownInterval = setInterval(() => {
                timeRemaining--;
                submitBtn.innerHTML = `Počkejte ${timeRemaining}s`;

                if (timeRemaining <= 0) {
                    clearInterval(countdownInterval);
                    submitBtn.innerHTML = 'Vypočítat';
                    submitBtn.disabled = false;
                    showResponse('Nyní můžete formulář znovu odeslat.', true);
                }
            }, 1000);

            return;
        }

        if (response.ok) {
            // Use the already parsed result

            // Check if email was sent automatically
            const emailStatus = result.emailSent ?
                '<p style="color: #28a745; font-weight: bold; margin-bottom: 15px;">✅ Email s PDF dokumenty byl automaticky odeslán na vaši adresu!</p>' :
                '<p style="color: #ffc107; font-weight: bold; margin-bottom: 15px;">⚠️ Email bude odeslán později administrátorem.</p>';

            // Create a summary message
            const summaryMessage = `
                <h3>Formulář byl úspěšně odeslán!</h3>
                ${emailStatus}
                <h3>Shrnutí kalkulace:</h3>
                <h4>OBLAST A – ZATEPLENÍ</h4>
                <p><strong>Zateplení fasády:</strong> ${facadeCost.toLocaleString()} Kč</p>
                <p><strong>Zateplení střechy:</strong> ${roofCost.toLocaleString()} Kč</p>
                <p><strong>Zateplení stropu pod nevytápěnou půdou:</strong> ${atticCost.toLocaleString()} Kč</p>
                <p><strong>Zateplení stěny mezi vytápěnou a nevytápěnou místností:</strong> ${wallCost.toLocaleString()} Kč</p>
                <p><strong>Konstrukce k zemině – zateplení podlahy:</strong> ${floorCost.toLocaleString()} Kč</p>
                <p><strong>Výplně otvorů – výměna oken a dveří:</strong> ${windowsCost.toLocaleString()} Kč</p>
                <p><strong>Stínící technika – venkovní žaluzie + MBox:</strong> ${shadingCost.toLocaleString()} Kč</p>

                <h4>OBLAST C – ZDROJE ENERGIE</h4>
                <p><strong>Výměna zdroje tepla – tepelné čerpadlo:</strong> ${heatPumpCost.toLocaleString()} Kč</p>
                <p><strong>Příprava teplé vody – fotovoltaický ohřev vody:</strong> ${photovoltaicCost.toLocaleString()} Kč</p>
                <p><strong>Fotovoltaické systémy pro výrobu el. energie:</strong> ${fveBatteryCost.toLocaleString()} Kč</p>

                <h4>BONUSY</h4>
                <p><strong>Bonus pro vybrané obce a regiony (5%):</strong> ${regionalBonusAmount.toLocaleString()} Kč</p>
                <p><strong>Rodinný bonus:</strong> ${familyBonusAmount.toLocaleString()} Kč</p>
                <p><strong>Kombinační bonus:</strong> ${combinationBonusAmount.toLocaleString()} Kč</p>
                <p><strong>Základní podpora:</strong> ${basicSupportAmount.toLocaleString()} Kč</p>
                <p><strong>Celková výše bonusů:</strong> ${totalBonuses.toLocaleString()} Kč</p>

                <h4>SHRNUTÍ</h4>
                <p><strong>Součet všech cen realizací (bez DPH):</strong> ${totalCost.toLocaleString()} Kč</p>
                <p><strong>Součet všech cen realizací x 1,12 (s DPH):</strong> ${totalCostWithVAT.toLocaleString()} Kč</p>
                <p><strong>Výše dotace Oblast A:</strong> ${(facadeSubsidy + roofSubsidy + atticSubsidy + wallSubsidy + floorSubsidy + windowsSubsidy + shadingSubsidy).toLocaleString()} Kč${(facadeSubsidy + roofSubsidy + atticSubsidy + wallSubsidy + floorSubsidy + windowsSubsidy + shadingSubsidy) >= 1000000 ? ' (dosažen limit 1 000 000 Kč)' : ''}</p>
                <p><strong>Výše dotace Oblast C:</strong> ${(heatPumpSubsidy + photovoltaicSubsidy + fveBatterySubsidy).toLocaleString()} Kč</p>
                <p><strong>Součet výše dotace + bonusy:</strong> ${totalSubsidy.toLocaleString()} Kč</p>
                <p><strong>Rozdíl = cena realizace – dotace a bonusy:</strong> ${finalCost.toLocaleString()} Kč</p>
                <p>${result.message}</p>
            `;

            responseMessage.innerHTML = summaryMessage;
            responseMessage.classList.add('success');
            responseMessage.style.display = 'block';
        } else {
            // Use the already parsed result
            showResponse(`Chyba: ${result.message}`, false);
        }
    } catch (error) {
        console.error('Error submitting form:', error);
        showResponse('Došlo k chybě. Zkuste to prosím znovu.', false);
    } finally {
        // Restore button state
        submitBtn.innerHTML = originalBtnText;
        submitBtn.disabled = false;
    }
});

// Function to calculate the total cost in real-time
// Function to calculate the total cost in real-time
function calculateRealTimeCost() {
    // Get facade insulation values
    const facadeAreaSubsidized = parseFloat(document.getElementById('facadeAreaSubsidized').value) || 0;
    const facadeAreaNonSubsidized = parseFloat(document.getElementById('facadeAreaNonSubsidized').value) || 0;
    const totalRevealLength = parseFloat(document.getElementById('totalRevealLength').value) || 0;
    const materialPolystyrene = document.getElementById('materialPolystyrene').checked;
    const materialMineralWool = document.getElementById('materialMineralWool').checked;

    // Get roof insulation values
    const roofAreaSubsidized = parseFloat(document.getElementById('roofAreaSubsidized').value) || 0;
    const roofAreaNonSubsidized = parseFloat(document.getElementById('roofAreaNonSubsidized').value) || 0;
    const additionalRoofWork = document.getElementById('additionalRoofWork').checked;

    // Get attic ceiling insulation values
    const atticAreaSubsidized = parseFloat(document.getElementById('atticAreaSubsidized').value) || 0;
    const atticAreaNonSubsidized = parseFloat(document.getElementById('atticAreaNonSubsidized').value) || 0;
    const atticFloorRemoval = document.getElementById('atticFloorRemoval').checked;
    const osbBoardArea = parseFloat(document.getElementById('osbBoardArea').value) || 0;
    const inspectionWalkwayLength = parseFloat(document.getElementById('inspectionWalkwayLength').value) || 0;

    // Get wall insulation values
    const wallAreaSubsidized = parseFloat(document.getElementById('wallAreaSubsidized').value) || 0;
    const wallAreaNonSubsidized = parseFloat(document.getElementById('wallAreaNonSubsidized').value) || 0;
    const wallSdkCladding = document.getElementById('wallSdkCladding').checked; // Added this
    const wallPlastering = document.getElementById('wallPlastering').checked; // Added this

    // Get floor insulation values
    const floorAreaSubsidized = parseFloat(document.getElementById('floorAreaSubsidized').value) || 0;
    const floorAreaNonSubsidized = parseFloat(document.getElementById('floorAreaNonSubsidized').value) || 0;

    // Get windows and doors values
    const windowsAreaSubsidized = parseFloat(document.getElementById('windowsAreaSubsidized').value) || 0;
    const windowsAreaNonSubsidized = parseFloat(document.getElementById('windowsAreaNonSubsidized').value) || 0;

    // Get shading technology values
    const shadingAreaSubsidized = parseFloat(document.getElementById('shadingAreaSubsidized').value) || 0;
    const shadingAreaNonSubsidized = parseFloat(document.getElementById('shadingAreaNonSubsidized').value) || 0;
    const totalWindowWidth = parseFloat(document.getElementById('totalWindowWidth').value) || 0;

    // Get heat pump values
    const heatPump8kW = document.getElementById('heatPump8kW').checked;
    const heatPump12kW = document.getElementById('heatPump12kW').checked;
    const heatPump16kW = document.getElementById('heatPump16kW').checked;
    const heatPump22kW = document.getElementById('heatPump22kW').checked;
    const heatPumpHeatingOnly = document.getElementById('heatPumpHeatingOnly').checked;
    const heatPumpHeatingWater = document.getElementById('heatPumpHeatingWater').checked;

    // Get photovoltaic water heating values
    const roofTypeSloped = document.getElementById('roofTypeSloped').checked;
    const roofTypeFlat = document.getElementById('roofTypeFlat').checked;
    const newBoilerYes = document.getElementById('newBoilerYes').checked;
    const newBoilerNo = document.getElementById('newBoilerNo').checked;

    // Get heat recovery values
    const heatRecoveryYes = document.getElementById('heatRecoveryYes').checked;
    const heatRecoveryNo = document.getElementById('heatRecoveryNo').checked;

    // Get rainwater values
    const rainwaterTankSize = parseFloat(document.getElementById('rainwaterTankSize').value) || 0; // Added this

    // Get bonus values (for real-time display of total subsidy/bonuses)
    const regionalBonus = document.getElementById('regionalBonus').checked;
    const childrenFullCare = parseInt(document.getElementById('childrenFullCare').value) || 0;
    const childrenPartialCare = parseInt(document.getElementById('childrenPartialCare').value) || 0;
    const combinationBonusInsulationFVE = document.getElementById('combinationBonusInsulationFVE').checked;
    const combinationBonusInsulationHeatSource = document.getElementById('combinationBonusInsulationHeatSource').checked;


    let cena = 0; // Main variable to accumulate total GROSS cost
    let totalSubsidy = 0; // Track total subsidy
    let totalBonuses = 0;

    // Facade calculations
    const totalFacadeArea = facadeAreaSubsidized + facadeAreaNonSubsidized;
    let facadeCost = 0;
    let facadeSubsidy = 0;

    if (materialPolystyrene) {
        facadeCost = (totalFacadeArea * pricingConfig.facade.polystyrene) + (totalRevealLength * pricingConfig.facade.revealCost);
    } else if (materialMineralWool) {
        facadeCost = (totalFacadeArea * pricingConfig.facade.mineralWool) + (totalRevealLength * pricingConfig.facade.revealCost);
    }
    facadeSubsidy = facadeAreaSubsidized * pricingConfig.facade.subsidy;
    cena += facadeCost; // Add GROSS cost to cena

    // Roof calculations
    const totalRoofArea = roofAreaSubsidized + roofAreaNonSubsidized;
    let roofCost = 0;
    let roofSubsidy = 0;

    if (additionalRoofWork) {
        roofCost = totalRoofArea * pricingConfig.roof.withCladding;
    } else {
        roofCost = totalRoofArea * pricingConfig.roof.withoutCladding;
    }
    roofSubsidy = roofAreaSubsidized * pricingConfig.roof.subsidy;
    cena += roofCost; // Add GROSS cost to cena

    // Attic ceiling calculations
    const totalAtticArea = atticAreaSubsidized + atticAreaNonSubsidized;
    let atticBaseCost = pricingConfig.attic.baseCost;
    let atticCost = 0;
    let atticSubsidy = 0;

    if (atticFloorRemoval) {
        atticBaseCost = pricingConfig.attic.withFloorRemoval;
    }
    atticCost = totalAtticArea * atticBaseCost;

    if (osbBoardArea > 0) {
        atticCost += osbBoardArea * pricingConfig.attic.osbBoardCost;
    }
    if (inspectionWalkwayLength > 0) {
        atticCost += inspectionWalkwayLength * pricingConfig.attic.walkwayCost;
    }
    atticSubsidy = atticAreaSubsidized * pricingConfig.attic.subsidy;
    cena += atticCost; // Add GROSS cost to cena

    // Wall insulation calculations
    const totalWallArea = wallAreaSubsidized + wallAreaNonSubsidized;
    let wallBaseCost = pricingConfig.wall.baseCost;
    let wallCost = 0;
    let wallSubsidy = 0;

    wallCost = totalWallArea * wallBaseCost;
    wallSubsidy = wallAreaSubsidized * pricingConfig.wall.subsidy;
    cena += wallCost; // Add GROSS cost to cena

    // Floor insulation calculations
    const totalFloorArea = floorAreaSubsidized + floorAreaNonSubsidized;
    let floorCost = totalFloorArea * pricingConfig.floor.baseCost;
    let floorSubsidy = floorAreaSubsidized * pricingConfig.floor.subsidy;
    cena += floorCost; // Add GROSS cost to cena

    // Windows and doors calculations
    const totalWindowsArea = windowsAreaSubsidized + windowsAreaNonSubsidized;
    let windowsCost = totalWindowsArea * pricingConfig.windows.baseCost;
    let windowsSubsidy = windowsAreaSubsidized * pricingConfig.windows.subsidy;
    cena += windowsCost; // Add GROSS cost to cena

    // Shading technology calculations
    const totalShadingArea = shadingAreaSubsidized + shadingAreaNonSubsidized;
    let shadingCost = (totalShadingArea * pricingConfig.shading.areaCost) + (totalWindowWidth * pricingConfig.shading.windowWidthCost);
    let shadingSubsidy = shadingAreaSubsidized * pricingConfig.shading.subsidy;
    cena += shadingCost; // Add GROSS cost to cena

    // Heat pump calculations
    let heatPumpCost = 0;
    let heatPumpSubsidy = 0;

    if (heatPump8kW) {
        heatPumpCost = pricingConfig.heatPump.power8kW;
    } else if (heatPump12kW) {
        heatPumpCost = pricingConfig.heatPump.power12kW;
    } else if (heatPump16kW) {
        heatPumpCost = pricingConfig.heatPump.power16kW;
    } else if (heatPump22kW) {
        heatPumpCost = pricingConfig.heatPump.power22kW;
    }
    if (heatPumpHeatingOnly) {
        heatPumpSubsidy = pricingConfig.heatPump.subsidyHeatingOnly;
    } else if (heatPumpHeatingWater) {
        heatPumpSubsidy = pricingConfig.heatPump.subsidyHeatingWater;
    }
    cena += heatPumpCost; // Add GROSS cost to cena

    // Photovoltaic water heating calculations
    let photovoltaicCost = 0;
    let photovoltaicSubsidy = 0;

    if (roofTypeSloped) {
        photovoltaicCost += pricingConfig.photovoltaic.slopedRoof;
    } else if (roofTypeFlat) {
        photovoltaicCost += pricingConfig.photovoltaic.flatRoof;
    }
    if (newBoilerYes) {
        photovoltaicCost += pricingConfig.photovoltaic.newBoiler;
    }
    if (roofTypeSloped || roofTypeFlat) {
        photovoltaicSubsidy = pricingConfig.photovoltaic.subsidy;
    }
    cena += photovoltaicCost; // Add GROSS cost to cena

    // FVE battery system calculations
    const fveBattery5_4 = document.getElementById('fveBattery5_4') ? document.getElementById('fveBattery5_4').checked : false;
    const fveBattery7_2 = document.getElementById('fveBattery7_2') ? document.getElementById('fveBattery7_2').checked : false;
    const fveBattery9_9 = document.getElementById('fveBattery9_9') ? document.getElementById('fveBattery9_9').checked : false;
    const fveBattery14_4 = document.getElementById('fveBattery14_4') ? document.getElementById('fveBattery14_4').checked : false;
    const wallboxCount = document.getElementById('wallboxCount') ? (parseInt(document.getElementById('wallboxCount').value) || 0) : 0;
    const gridConnectionYes = document.getElementById('gridConnectionYes') ? document.getElementById('gridConnectionYes').checked : false;

    let fveBatteryCost = 0;
    let fveBatterySubsidy = 0;

    if (fveBattery5_4) {
        fveBatteryCost = pricingConfig.fveBattery.system5_4kWp;
    } else if (fveBattery7_2) {
        fveBatteryCost = pricingConfig.fveBattery.system7_2kWp;
    } else if (fveBattery9_9) {
        fveBatteryCost = pricingConfig.fveBattery.system9_9kWp;
    } else if (fveBattery14_4) {
        fveBatteryCost = pricingConfig.fveBattery.system14_4kWp;
    }

    // Add wallbox costs
    if (wallboxCount > 0) {
        fveBatteryCost += wallboxCount * pricingConfig.fveBattery.wallboxCost;
    }

    // Calculate FVE battery subsidies
    if (fveBattery5_4 || fveBattery7_2 || fveBattery9_9 || fveBattery14_4) {
        fveBatterySubsidy = pricingConfig.fveBattery.baseSubsidy; // Base subsidy for FVE
        if (gridConnectionYes) {
            fveBatterySubsidy += pricingConfig.fveBattery.gridSubsidy; // Additional subsidy for grid connection
        }
    }

    cena += fveBatteryCost; // Add GROSS cost to cena

    // Heat recovery calculations
    let heatRecoveryCost = 0;
    let heatRecoverySubsidy = 0;

    if (heatRecoveryYes) {
        heatRecoveryCost = pricingConfig.heatRecovery.baseCost;
        heatRecoverySubsidy = pricingConfig.heatRecovery.subsidy;
    }
    cena += heatRecoveryCost; // Add GROSS cost to cena

    // Rainwater calculations
    let rainwaterCost = 0;
    let rainwaterSubsidy = 0;

    if (rainwaterTankSize > 0) {
        // Cost = base cost + (tank size × cost per m³)
        rainwaterCost = pricingConfig.rainwater.baseCost + (rainwaterTankSize * pricingConfig.rainwater.costPerM3);
        // Subsidy = base subsidy + (tank size × subsidy per m³)
        rainwaterSubsidy = pricingConfig.rainwater.baseSubsidy + (rainwaterTankSize * pricingConfig.rainwater.subsidyPerM3);
    }
    cena += rainwaterCost; // Add GROSS cost to cena

    // Total subsidy calculation for Area A (before limit application)
    let areaASubsidy = facadeSubsidy + roofSubsidy + atticSubsidy + wallSubsidy + floorSubsidy + windowsSubsidy + shadingSubsidy;

    // Apply maximum subsidy limit of 1,000,000 CZK for Area A
    if (areaASubsidy > 1000000) {
        // Since we're calculating totalSubsidy at the end, we just cap this component
        // The reduction factor logic is only needed if you want to proportionally reduce
        // each individual subsidy in Area A. For total calculation, capping the sum is sufficient.
        areaASubsidy = 1000000;
    }

    // Calculate base subsidies (Area A + Area C + Area D)
    const baseSubsidies = areaASubsidy + heatPumpSubsidy + photovoltaicSubsidy + fveBatterySubsidy + heatRecoverySubsidy + rainwaterSubsidy;

    // Calculate bonuses
    const basicSupportAmount = pricingConfig.bonuses.basicSupport; // Automatic basic support
    const familyBonusAmount = (childrenFullCare * pricingConfig.bonuses.childFullCare) + (childrenPartialCare * pricingConfig.bonuses.childPartialCare);
    let combinationBonusAmount = 0;

    if (combinationBonusInsulationFVE) {
        combinationBonusAmount += pricingConfig.bonuses.combinationInsulationFVE;
    }
    if (combinationBonusInsulationHeatSource) {
        combinationBonusAmount += pricingConfig.bonuses.combinationInsulationHeatSource;
    }
    let regionalBonusAmount = 0;
    if (regionalBonus) {
        regionalBonusAmount = baseSubsidies * (pricingConfig.bonuses.regionalBonusPercent / 100);
    }
    totalBonuses = regionalBonusAmount + familyBonusAmount + combinationBonusAmount + basicSupportAmount;

    // Calculate total subsidy including all bonuses
    totalSubsidy = baseSubsidies + totalBonuses;


    // Final calculations for display
    const totalCost = cena; // cena now represents the total gross cost
    const totalCostWithVAT = totalCost * 1.12;
    const finalCost = totalCostWithVAT - totalSubsidy;

    // Update summary fields in the form
    document.getElementById('totalCostWithoutVAT').textContent = totalCost.toLocaleString() + ' Kč';
    document.getElementById('totalCostWithVAT').textContent = totalCostWithVAT.toLocaleString() + ' Kč';
    document.getElementById('totalSubsidyWithBonuses').textContent = totalSubsidy.toLocaleString() + ' Kč';
    document.getElementById('finalDifference').textContent = finalCost.toLocaleString() + ' Kč';


}

// Add event listeners to all form inputs
function addFormInputListeners() {
    // Get all input elements in the form
    const inputs = document.querySelectorAll('#contactForm input');

    // Add change event listener to each input
    inputs.forEach(input => {
        input.addEventListener('input', calculateRealTimeCost);
        input.addEventListener('change', calculateRealTimeCost);
    });

    // Special handling for OSB board area and inspection walkway length
    const osbBoardArea = document.getElementById('osbBoardArea');
    const inspectionWalkwayLength = document.getElementById('inspectionWalkwayLength');

    // When OSB board area changes
    osbBoardArea.addEventListener('input', function() {
        if (this.value && parseFloat(this.value) > 0) {
            // If OSB board area has a value, automatically set inspection walkway to 0
            inspectionWalkwayLength.value = '0';
            updateMutualExclusiveStyles();
        }
        calculateRealTimeCost();
    });

    // When inspection walkway length changes
    inspectionWalkwayLength.addEventListener('input', function() {
        if (this.value && parseFloat(this.value) > 0) {
            // If inspection walkway has a value, automatically set OSB board area to 0
            osbBoardArea.value = '0';
            updateMutualExclusiveStyles();
        }
        calculateRealTimeCost();
    });



    // Make ALL radio buttons toggleable (able to be unselected)
    const allRadios = document.querySelectorAll('input[type="radio"]');

    allRadios.forEach(radio => {
        radio.addEventListener('click', function() {
            // If this radio button is already checked, uncheck it
            if (this.getAttribute('data-previously-checked') === 'true') {
                this.checked = false;
                this.setAttribute('data-previously-checked', 'false');

                // Handle mutual exclusion between heat pump and photovoltaic sections
                handleMutualExclusion(this);
            } else {
                // Mark all radio buttons in the same group as unchecked
                document.querySelectorAll(`input[name="${this.name}"]`).forEach(r => {
                    r.setAttribute('data-previously-checked', 'false');
                });
                // Mark this one as checked
                this.setAttribute('data-previously-checked', 'true');

                // Handle mutual exclusion between heat pump and photovoltaic sections
                handleMutualExclusion(this);
            }
            calculateRealTimeCost();
        });
    });

    // Function to handle mutual exclusion between heat pump and photovoltaic sections
    function handleMutualExclusion(clickedRadio) {
        const heatPumpPowerRadios = ['heatPump8kW', 'heatPump12kW', 'heatPump16kW', 'heatPump22kW'];
        const heatPumpUsageRadios = ['heatPumpHeatingOnly', 'heatPumpHeatingWater'];
        const photovoltaicRadios = ['roofTypeSloped', 'roofTypeFlat', 'newBoilerYes', 'newBoilerNo'];

        const clickedId = clickedRadio.id;

        // Check if "Vytápění + ohřev vody" is selected
        const heatPumpHeatingWater = document.getElementById('heatPumpHeatingWater');
        const isHeatingWaterSelected = heatPumpHeatingWater && heatPumpHeatingWater.checked;

        // If heat pump power was selected and "Vytápění + ohřev vody" is selected, clear photovoltaic options
        if (heatPumpPowerRadios.includes(clickedId) && clickedRadio.checked && isHeatingWaterSelected) {
            photovoltaicRadios.forEach(radioId => {
                const radio = document.getElementById(radioId);
                if (radio) {
                    radio.checked = false;
                    radio.setAttribute('data-previously-checked', 'false');
                }
            });
        }

        // If "Vytápění + ohřev vody" was selected, clear all photovoltaic options
        if (clickedId === 'heatPumpHeatingWater' && clickedRadio.checked) {
            photovoltaicRadios.forEach(radioId => {
                const radio = document.getElementById(radioId);
                if (radio) {
                    radio.checked = false;
                    radio.setAttribute('data-previously-checked', 'false');
                }
            });
        }

        // If a photovoltaic option was selected and "Vytápění + ohřev vody" is selected, clear heat pump options
        if (photovoltaicRadios.includes(clickedId) && clickedRadio.checked && isHeatingWaterSelected) {
            [...heatPumpPowerRadios, ...heatPumpUsageRadios].forEach(radioId => {
                const radio = document.getElementById(radioId);
                if (radio) {
                    radio.checked = false;
                    radio.setAttribute('data-previously-checked', 'false');
                }
            });
        }

        // If a photovoltaic option was selected, clear "Vytápění + ohřev vody" but allow "Pouze vytápění"
        if (photovoltaicRadios.includes(clickedId) && clickedRadio.checked) {
            if (heatPumpHeatingWater) {
                heatPumpHeatingWater.checked = false;
                heatPumpHeatingWater.setAttribute('data-previously-checked', 'false');
            }
        }
    }
}

// Function to update visual styles for mutual exclusive fields
function updateMutualExclusiveStyles() {
    const osbBoardArea = document.getElementById('osbBoardArea');
    const inspectionWalkwayLength = document.getElementById('inspectionWalkwayLength');

    if (!osbBoardArea || !inspectionWalkwayLength) return;

    const osbValue = parseFloat(osbBoardArea.value) || 0;
    const walkwayValue = parseFloat(inspectionWalkwayLength.value) || 0;

    // Update OSB board styling
    if (osbValue > 0) {
        osbBoardArea.style.backgroundColor = '#ffffff';
        osbBoardArea.style.borderColor = '#1e3a8a';
        osbBoardArea.style.color = '#1f2937';
    } else {
        osbBoardArea.style.backgroundColor = '#f8fafc';
        osbBoardArea.style.borderColor = '#ddd';
        osbBoardArea.style.color = '#6b7280';
    }

    // Update inspection walkway styling
    if (walkwayValue > 0) {
        inspectionWalkwayLength.style.backgroundColor = '#ffffff';
        inspectionWalkwayLength.style.borderColor = '#1e3a8a';
        inspectionWalkwayLength.style.color = '#1f2937';
    } else {
        inspectionWalkwayLength.style.backgroundColor = '#f8fafc';
        inspectionWalkwayLength.style.borderColor = '#ddd';
        inspectionWalkwayLength.style.color = '#6b7280';
    }
}

// Function to initialize the form fields
function initializeFormFields() {
    // Check initial state of OSB board area and inspection walkway
    const osbBoardArea = document.getElementById('osbBoardArea');
    const inspectionWalkwayLength = document.getElementById('inspectionWalkwayLength');

    // Set initial values to 0 if both are empty for better UX
    if (!osbBoardArea.value) {
        osbBoardArea.value = '0';
    }
    if (!inspectionWalkwayLength.value) {
        inspectionWalkwayLength.value = '0';
    }

    // Apply initial styling
    updateMutualExclusiveStyles();
}

// Check auth status when page loads
document.addEventListener('DOMContentLoaded', () => {
    initializeApp();

    // Add event listeners after the DOM is loaded
    addFormInputListeners();

    // Initialize form fields
    initializeFormFields();

    // Initialize real-time cost calculation
    calculateRealTimeCost();

    // Restrict phone input to numbers, spaces, and + only
    const phoneInput = document.getElementById('phone');
    if (phoneInput) {
        phoneInput.addEventListener('input', function(e) {
            // Allow only numbers, spaces, and + sign
            this.value = this.value.replace(/[^0-9\s\+]/g, '');
        });
    }
});