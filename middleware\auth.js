module.exports = {
  // Ensure user is authenticated
  ensureAuth: function(req, res, next) {
    console.log('ensureAuth - isAuthenticated:', req.isAuthenticated());
    console.log('ensureAuth - user:', req.user);

    if (req.isAuthenticated()) {
      return next();
    }
    console.log('Not authenticated, redirecting to login');
    res.redirect('/login');
  },

  // Check for specific role
  checkRole: function(roles) {
    return (req, res, next) => {
      console.log('checkRole - isAuthenticated:', req.isAuthenticated());
      console.log('checkRole - user role:', req.user ? req.user.role : 'no user');
      console.log('checkRole - allowed roles:', roles);

      if (!req.isAuthenticated()) {
        console.log('Not authenticated, redirecting to login');
        return res.redirect('/login');
      }

      if (!roles.includes(req.user.role)) {
        console.log('Access denied - user role not in allowed roles');
        return res.status(403).send('Access denied. You do not have permission to view this page.');
      }

      console.log('Access granted');
      next();
    };
  }
};
