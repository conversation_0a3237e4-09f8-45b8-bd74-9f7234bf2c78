/**
 * PDF Text Positioning Configuration
 *
 * This file contains all the positioning coordinates for text overlays in PDF contracts.
 * Adjust these values to fix text alignment issues.
 *
 * COORDINATE SYSTEM:
 * - X: 0 is left edge, increases going right
 * - Y: 0 is top edge, increases going down
 * - Template images are 1645x2339 pixels
 *
 * HOW TO ADJUST:
 * 1. Generate a test PDF
 * 2. Check console logs for current coordinates
 * 3. Adjust values below based on where text should be positioned
 * 4. Restart server and test again
 */

module.exports = {
    // SMLOUVA1 TEMPLATE POSITIONING
    smlouva1: {
        // FIRST PAGE (Page 1) - Customer Information Section
        firstPage: {
        // === CUSTOMER PERSONAL INFORMATION ===

        // Customer name field (Jméno a příjmení)
        customerName: {
            x: 210,           // X position after "Jméno a příjmení:" label
            y: 414.5,           // Y position for customer name (413 + 1)
            fontSize: 10,     // Font size
            color: '#000000', // Text color
            maxWidth: 300     // Maximum text width
        },

        // Customer address field (Bydliště)
        customerAddress: {
            x: 210,           // X position after "Bydliště:" label
            y: 434.3,         // Y position for address (433.3 + 1)
            fontSize: 10,
            color: '#000000',
            maxWidth: 300
        },

        // Date of birth field (Datum narození)
        dateOfBirth: {
            x: 210,           // X position after "Datum narození:" label
            y: 453.6,         // Y position for date of birth (452.6 + 1)
            fontSize: 10,
            color: '#000000',
            maxWidth: 200
        },

        // Email field (e-mail)
        email: {
            x: 210,           // X position after "e-mail, tel.:" label
            y: 472,           // Y position for email (471 + 1)
            fontSize: 10,
            color: '#000000',
            maxWidth: 200     // If email exceeds this width, phone moves to next line
        },

        // Phone field (telefon) - dynamically positioned to avoid email overlap
        phone: {
            x: 350,           // Minimum X position (will be adjusted if email is long)
            y: 472,           // Y position (471 + 1)
            fontSize: 10,
            color: '#000000',
            maxWidth: 150
        },

        // === PROJECT INFORMATION ===

        // Realization address field (adresa realizace)
        realizationAddress: {
            x: 250,           // X position after "nacházející se na adrese:" label
            y: 703,           // Y position for property address (703 + 1)
            fontSize: 10,
            color: '#000000',
            maxWidth: 300
        },

        // Project description field
        projectDescription: {
            x: 70,            // X position for project description
            y: 520,           // Y position for project description
            fontSize: 9,
            color: '#000000',
            maxWidth: 450
        },

        // Work scope field
        workScope: {
            x: 70,            // X position for work scope
            y: 580,           // Y position for work scope
            fontSize: 9,
            color: '#000000',
            maxWidth: 450
        },

        // === FINANCIAL INFORMATION ===

        // Total cost field
        totalCost: {
            x: 400,           // X position for total cost
            y: 650,           // Y position for total cost
            fontSize: 10,
            color: '#000000',
            maxWidth: 120,
            align: 'right'
        },

        // Total cost with VAT field
        totalCostWithVAT: {
            x: 400,           // X position for total cost with VAT
            y: 670,           // Y position for total cost with VAT
            fontSize: 10,
            color: '#000000',
            maxWidth: 120,
            align: 'right'
        },

        // Total subsidy field
        totalSubsidy: {
            x: 400,           // X position for total subsidy
            y: 690,           // Y position for total subsidy
            fontSize: 10,
            color: '#000000',
            maxWidth: 120,
            align: 'right'
        },

        // Final cost field (after subsidies)
        finalCost: {
            x: 400,           // X position for final cost
            y: 710,           // Y position for final cost
            fontSize: 12,
            color: '#000000',
            maxWidth: 120,
            align: 'right',
            fontWeight: 'bold'
        },

        // === CONTRACT DETAILS ===

        // Contract number field
        contractNumber: {
            x: 450,           // X position for contract number
            y: 150,           // Y position for contract number
            fontSize: 10,
            color: '#000000',
            maxWidth: 100
        },

        // Contract date field
        contractDate: {
            x: 450,           // X position for contract date
            y: 170,           // Y position for contract date
            fontSize: 10,
            color: '#000000',
            maxWidth: 100
        },

        // Validity period field
        validityPeriod: {
            x: 450,           // X position for validity period
            y: 190,           // Y position for validity period
            fontSize: 10,
            color: '#000000',
            maxWidth: 100
        }
    },

    // SECOND PAGE (Page 2) - Technical Specifications
    secondPage: {
        // === FACADE INSULATION ===

        // Facade material type
        facadeMaterial: {
            x: 150,           // X position for facade material
            y: 200,           // Y position for facade material
            fontSize: 9,
            color: '#000000',
            maxWidth: 200
        },

        // Facade subsidized area
        facadeSubsidizedArea: {
            x: 350,           // X position for subsidized area
            y: 220,           // Y position for subsidized area
            fontSize: 9,
            color: '#000000',
            maxWidth: 80,
            align: 'right'
        },

        // Facade non-subsidized area
        facadeNonSubsidizedArea: {
            x: 450,           // X position for non-subsidized area
            y: 220,           // Y position for non-subsidized area
            fontSize: 9,
            color: '#000000',
            maxWidth: 80,
            align: 'right'
        },

        // === HEAT PUMP INFORMATION ===

        // Heat pump type
        heatPumpType: {
            x: 150,           // X position for heat pump type
            y: 300,           // Y position for heat pump type
            fontSize: 9,
            color: '#000000',
            maxWidth: 200
        },

        // Heat pump power
        heatPumpPower: {
            x: 350,           // X position for heat pump power
            y: 320,           // Y position for heat pump power
            fontSize: 9,
            color: '#000000',
            maxWidth: 100
        },

        // === PHOTOVOLTAIC INFORMATION ===

        // PV system type
        pvSystemType: {
            x: 150,           // X position for PV system type
            y: 400,           // Y position for PV system type
            fontSize: 9,
            color: '#000000',
            maxWidth: 200
        },

        // PV power rating
        pvPowerRating: {
            x: 350,           // X position for PV power rating
            y: 420,           // Y position for PV power rating
            fontSize: 9,
            color: '#000000',
            maxWidth: 100
        },

        // Battery capacity
        batteryCapacity: {
            x: 450,           // X position for battery capacity
            y: 420,           // Y position for battery capacity
            fontSize: 9,
            color: '#000000',
            maxWidth: 100
        }
    },

    // LAST PAGE (Page 6) - Signature Section
    lastPage: {
        // === CONTRACTOR SIGNATURE ===

        // Contractor name
        contractorName: {
            x: 120,           // X position for contractor name
            y: 320,           // Y position for contractor name
            fontSize: 10,
            color: '#000000',
            maxWidth: 150,
            align: 'center'
        },

        // Contractor title
        contractorTitle: {
            x: 120,           // X position for contractor title
            y: 320,           // Y position for contractor title
            fontSize: 9,
            color: '#000000',
            maxWidth: 150,
            align: 'center'
        },

        // Contractor signature date
        contractorSignatureDate: {
            x: 120,           // X position for contractor signature date
            y: 320,           // Y position for contractor signature date
            fontSize: 9,
            color: '#000000',
            maxWidth: 150,
            align: 'center'
        },

        // === CUSTOMER SIGNATURE ===

        // Customer signature name
        customerSignature: {
            x: 360,           // X position for customer signature
            y: 344,           // Y position for customer signature (345 + 1)
            fontSize: 10,
            color: '#000000',
            maxWidth: 150,
            align: 'center'
        },

        // Customer signature date
        customerSignatureDate: {
            x: 360,           // X position for customer signature date
            y: 375,           // Y position for customer signature date (below name)
            fontSize: 9,
            color: '#000000',
            maxWidth: 150,
            align: 'center'
        },

        // === ADDITIONAL SIGNATURE FIELDS ===

        // Witness signature (if needed)
        witnessSignature: {
            x: 240,           // X position for witness signature
            y: 400,           // Y position for witness signature
            fontSize: 9,
            color: '#000000',
            maxWidth: 150,
            align: 'center'
        },

        // Document stamp position
        documentStamp: {
            x: 450,           // X position for document stamp
            y: 300,           // Y position for document stamp
            fontSize: 8,
            color: '#000000',
            maxWidth: 100,
            align: 'center'
        }
    },

    // GLOBAL SETTINGS
    global: {
        // Default font settings
        defaultFont: {
            family: 'NotoSans',  // Font family (fallback to Helvetica)
            size: 10,            // Default font size
            color: '#000000'     // Default text color
        },

        // Page dimensions (A4)
        pageSize: {
            width: 595.28,       // A4 width in points
            height: 841.89       // A4 height in points
        },

        // Margins
        margins: {
            top: 50,
            bottom: 50,
            left: 50,
            right: 50
        }
    },

        // DEBUG SETTINGS
        debug: {
            logCoordinates: true,    // Set to false to disable coordinate logging
            showBorders: false,      // Set to true to show text boundaries (for debugging)
            highlightFields: false,  // Set to true to highlight all text fields with colored backgrounds
            logFieldData: true       // Set to true to log the data being inserted into each field
        }
    },

    // SMLOUVA2 TEMPLATE POSITIONING (copy of smlouva1 with adjustments)
    smlouva2: {
        // FIRST PAGE (Page 1) - Customer Information Section
        firstPage: {
            // === CUSTOMER PERSONAL INFORMATION ===

            // Customer name field (Jméno a příjmení)
            customerName: {
                x: 210,           // X position after "Jméno a příjmení:" label
                y: 414,           // Y position for customer name
                fontSize: 10,     // Font size
                color: '#000000', // Text color
                maxWidth: 300     // Maximum text width
            },

            // Customer address field (Bydliště)
            customerAddress: {
                x: 210,           // X position after "Bydliště:" label
                y: 434.3,         // Y position for address
                fontSize: 10,
                color: '#000000',
                maxWidth: 300
            },

            // Date of birth field (Datum narození)
            dateOfBirth: {
                x: 210,           // X position after "Datum narození:" label
                y: 453.6,         // Y position for date of birth
                fontSize: 10,
                color: '#000000',
                maxWidth: 200
            },

            // Email field (e-mail)
            email: {
                x: 210,           // X position after "e-mail, tel.:" label
                y: 472,           // Y position for email
                fontSize: 10,
                color: '#000000',
                maxWidth: 200     // If email exceeds this width, phone moves to next line
            },

            // Phone field (telefon) - dynamically positioned to avoid email overlap
            phone: {
                x: 350,           // Minimum X position (will be adjusted if email is long)
                y: 472,           // Y position
                fontSize: 10,
                color: '#000000',
                maxWidth: 150
            },

            // Realization address field (adresa realizace)
            realizationAddress: {
                x: 250,           // X position after "nacházející se na adrese:" label
                y: 704,           // Y position for property address
                fontSize: 10,
                color: '#000000',
                maxWidth: 300
            }
        },

        // LAST PAGE (Page 6) - Signature Section
        lastPage: {
            // Customer signature name - THIS IS WHERE YOU ADJUST THE RIGHT-SIDE NAME POSITION
            customerSignature: {
                x: 360,           // X position for customer signature (CHANGE THIS NUMBER)
                y: 330,           // Y position for customer signature (CHANGE THIS NUMBER)
                fontSize: 10,
                color: '#000000',
                maxWidth: 150,
                align: 'center'
            }
        },

        // GLOBAL SETTINGS
        global: {
            // Default font settings
            defaultFont: {
                family: 'NotoSans',  // Font family (fallback to Helvetica)
                size: 10,            // Default font size
                color: '#000000'     // Default text color
            },

            // Page dimensions (A4)
            pageSize: {
                width: 595.28,       // A4 width in points
                height: 841.89       // A4 height in points
            },

            // Margins
            margins: {
                top: 50,
                bottom: 50,
                left: 50,
                right: 50
            }
        },

        // DEBUG SETTINGS
        debug: {
            logCoordinates: true,    // Set to false to disable coordinate logging
            showBorders: false,      // Set to true to show text boundaries (for debugging)
            highlightFields: false,  // Set to true to highlight all text fields with colored backgrounds
            logFieldData: true       // Set to true to log the data being inserted into each field
        }
    }
};

/**
 * COMMON ADJUSTMENT SCENARIOS:
 *
 * Text too far right: DECREASE X values
 * Text too far left: INCREASE X values
 * Text too high: INCREASE Y values
 * Text too low: DECREASE Y values
 * Lines too close together: INCREASE lineHeight
 * Lines too far apart: DECREASE lineHeight
 * Text too small: INCREASE font.size
 * Text too large: DECREASE font.size
 *
 * EXAMPLE ADJUSTMENTS:
 * If customer name appears too far to the right:
 * Change customerName.x from 260 to 200
 *
 * If lines are overlapping:
 * Change lineHeight values or Y positions
 *
 * If signature is too high:
 * Change customerSignature.y from 341 to 380
 *
 * FIELD-SPECIFIC ADJUSTMENTS:
 * - customerName: Main customer name on first page
 * - customerAddress: Customer's home address
 * - dateOfBirth: Customer's birth date (DD.MM.YYYY format)
 * - email: Customer's email address
 * - phone: Customer's phone number
 * - realizationAddress: Project/property address
 * - projectDescription: Description of work to be done
 * - workScope: Scope of work details
 * - totalCost: Total project cost before VAT
 * - totalCostWithVAT: Total cost including VAT
 * - totalSubsidy: Total subsidy amount
 * - finalCost: Final cost after subsidies
 * - contractNumber: Contract identification number
 * - contractDate: Date contract was created
 * - validityPeriod: How long the contract is valid
 * - facadeMaterial: Type of facade insulation material
 * - facadeSubsidizedArea: Subsidized facade area in m²
 * - facadeNonSubsidizedArea: Non-subsidized facade area in m²
 * - heatPumpType: Type of heat pump system
 * - heatPumpPower: Heat pump power rating in kW
 * - pvSystemType: Photovoltaic system type
 * - pvPowerRating: PV system power in kWp
 * - batteryCapacity: Battery storage capacity in kWh
 * - contractorName: Name of contractor/company
 * - contractorTitle: Title of contractor representative
 * - contractorSignatureDate: Date contractor signed
 * - customerSignature: Customer name for signature
 * - customerSignatureDate: Date customer signed
 * - witnessSignature: Witness signature if required
 * - documentStamp: Position for official stamp
 */
