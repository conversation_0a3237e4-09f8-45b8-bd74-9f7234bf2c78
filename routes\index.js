const express = require('express');
const router = express.Router();
const { ensureAuth, checkRole } = require('../middleware/auth');
const path = require('path');
const fs = require('fs');

// Public routes
router.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, '../index.html'));
});

router.get('/login', (req, res) => {
  if (req.isAuthenticated()) {
    return res.redirect('/dashboard');
  }
  res.sendFile(path.join(__dirname, '../views/login.html'));
});

// Debug route - DISABLED IN PRODUCTION
router.get('/debug', (req, res) => {
  if (process.env.NODE_ENV === 'production') {
    return res.status(404).send('Not found');
  }
  res.json({
    isAuthenticated: req.isAuthenticated(),
    userRole: req.user?.role || 'none'
  });
});

// Dashboard route
router.get('/dashboard', ensureAuth, (req, res) => {
  res.sendFile(path.join(__dirname, '../views/dashboard.html'));
});

// Helper function to fix Google profile image URLs
function fixGoogleProfileImageUrl(profileImage) {
  if (!profileImage || !profileImage.includes('googleusercontent.com')) {
    return profileImage;
  }

  // Remove size parameter and add a reliable size
  const baseUrl = profileImage.split('=')[0];
  return baseUrl + '=s96-c';
}

// User data route (for client-side rendering)
router.get('/api/user', ensureAuth, (req, res) => {
  res.json({
    id: req.user.id,
    displayName: req.user.displayName,
    email: req.user.email,
    profileImage: fixGoogleProfileImageUrl(req.user.profileImage),
    role: req.user.role
  });
});

// Admin route
router.get('/admin', ensureAuth, checkRole(['admin']), (req, res) => {
  res.sendFile(path.join(__dirname, '../views/admin.html'));
});

// Submissions route
router.get('/submissions', ensureAuth, checkRole(['editor', 'admin']), (req, res) => {
  res.sendFile(path.join(__dirname, '../views/submissions.html'));
});

// Admin submissions route
router.get('/admin/submissions', ensureAuth, checkRole(['admin']), (req, res) => {
  res.sendFile(path.join(__dirname, '../views/submissions.html'));
});

// Logo settings route
router.get('/logo-settings', ensureAuth, checkRole(['editor', 'admin']), (req, res) => {
  res.sendFile(path.join(__dirname, '../views/logo-settings.html'));
});

// Pricing settings route
router.get('/pricing-settings', ensureAuth, checkRole(['editor', 'admin']), (req, res) => {
  res.sendFile(path.join(__dirname, '../views/pricing-settings.html'));
});

// Test submissions route
router.get('/test-submissions', ensureAuth, checkRole(['editor', 'admin']), (req, res) => {
  res.sendFile(path.join(__dirname, '../views/test-submissions.html'));
});

// Calculator route
router.get('/calculator', (req, res) => {
  res.sendFile(path.join(__dirname, '../calculator.html'));
});

module.exports = router;
