/**
 * Test script for PDF text positioning
 * 
 * This script helps you test and adjust text positioning in contract PDFs
 * without having to go through the full form submission process.
 */

const documentService = require('./services/documentService');

// Test data with Czech characters
const testFormData = {
    customerName: '<PERSON>',
    customerAddress: 'Testovací ulice 123, Praha 1, 110 00',
    dateOfBirth: '01.01.1980',
    email: '<EMAIL>',
    phone: '+420 123 456 789',
    realizationAddress: 'Realizační adresa 456, Brno, 602 00'
};

async function testPositioning() {
    console.log('🧪 Testing PDF text positioning...');
    console.log('=====================================');

    try {
        
        const outputName = 'test_positioning_' + Date.now();
        console.log('Generating test contract:', outputName);
        
        const result = await documentService.generateImageBasedContract(outputName, testFormData);
        
        if (result.success) {
            console.log('✅ Test contract generated successfully!');
            console.log('📄 PDF file:', result.pdfPath);
            
            // Check file size
            const fs = require('fs');
            if (fs.existsSync(result.pdfPath)) {
                const stats = fs.statSync(result.pdfPath);
                console.log('📊 File size:', Math.round(stats.size / 1024), 'KB');
            }
            
            console.log('');
            console.log('📋 NEXT STEPS:');
            console.log('1. Open the generated PDF file');
            console.log('2. Check if text is positioned correctly');
            console.log('3. If not, edit config/pdf-positioning.js');
            console.log('4. Run this test again: node test-pdf-positioning.js');
            console.log('');
            console.log('💡 POSITIONING TIPS:');
            console.log('- Text too far right? DECREASE X values');
            console.log('- Text too far left? INCREASE X values');
            console.log('- Text too high? INCREASE Y values');
            console.log('- Text too low? DECREASE Y values');
            console.log('- Lines overlapping? INCREASE lineHeight');
            console.log('- Text too small? INCREASE font.size');
            console.log('');
            console.log('📝 Configuration file: config/pdf-positioning.js');
            console.log('🔧 Current settings are logged in console during generation');
            
        } else {
            console.log('❌ Test failed:', result.message);
        }
        
    } catch (error) {
        console.error('❌ Error:', error);
    }
}

testPositioning().then(() => {
    console.log('Test completed');
    process.exit(0);
}).catch(error => {
    console.error('Test failed:', error);
    process.exit(1);
});
