# Railway Deployment Guide - Email Configuration Fix

## 🚀 **Environment Variables Required**

Set these environment variables in your Railway dashboard:

### **Required Variables:**
```
NODE_ENV=production
BREVO_SMTP_USER=<EMAIL>
BREVO_SMTP_PASS=mhW7OcR9E8sx2BjG
SESSION_SECRET=your_strong_random_secret_here
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
FIREBASE_ADMIN_KEY=your_firebase_admin_key_json
```

## 🔧 **How to Set Environment Variables on Railway**

1. **Go to your Railway project dashboard**
2. **Click on your service**
3. **Go to the "Variables" tab**
4. **Add each variable one by one:**
   - Click "New Variable"
   - Enter the name (e.g., `BREVO_SMTP_USER`)
   - Enter the value (e.g., `<EMAIL>`)
   - Click "Add"

## 📧 **Email Configuration Changes Made**

### **Production Optimizations:**
- **Longer timeouts**: 3 minutes for email sending in production
- **Better connection handling**: Optimized for Railway's network
- **Enhanced error logging**: More detailed error messages
- **Startup verification**: Email config is tested when server starts

### **Key Improvements:**
1. **Connection timeout**: 2 minutes (vs 1 minute locally)
2. **Socket timeout**: 2 minutes (vs 1 minute locally)
3. **Email timeout**: 3 minutes (vs 2 minutes locally)
4. **TLS verification**: Enabled in production
5. **Connection pooling**: Optimized for production

## 🔍 **Debugging Steps**

### **1. Check Railway Logs**
After deployment, check the Railway logs for:
```
✅ Email configuration verified successfully
```

If you see:
```
❌ Email configuration verification failed
```

Then the environment variables are not set correctly.

### **2. Test Email Sending**
Submit a form and check the logs for:
```
✅ Email with PDF(s) sent via Brevo SMTP to: [email]
```

### **3. Common Issues & Solutions**

**Issue**: `Email configuration verification failed`
**Solution**: Check that `BREVO_SMTP_USER` and `BREVO_SMTP_PASS` are set correctly

**Issue**: `Email sending timeout after 180 seconds`
**Solution**: This indicates network issues. The timeout has been increased to handle this.

**Issue**: `Authentication failed`
**Solution**: Verify the Brevo credentials are correct and active

## 🚀 **Deployment Steps**

1. **Set all environment variables** in Railway dashboard
2. **Deploy the updated code** (push to your connected Git repository)
3. **Check the deployment logs** for email verification success
4. **Test form submission** to verify emails are working

## 📊 **Expected Performance**

- **Local**: ~30-35 seconds for form submission with email
- **Railway**: ~45-60 seconds (due to network latency and larger timeouts)

This is normal for:
- Generating 2 large PDFs (3-4MB each)
- Sending email with attachments
- Network latency in production

## 🔧 **Troubleshooting Commands**

If emails still fail, you can check the Railway logs for specific error messages:

1. **Authentication errors**: Check SMTP credentials
2. **Timeout errors**: Network issues (already handled with longer timeouts)
3. **Attachment errors**: PDF generation issues (should be rare)

## ✅ **Success Indicators**

You'll know it's working when you see in Railway logs:
```
✅ Email configuration verified successfully
✅ Email with PDF(s) sent via Brevo SMTP to: [email]
Automatic email sent successfully
```

And the user receives the success message:
```
Formulář byl úspěšně odeslán! Email s PDF dokumenty byl automaticky odeslán na vaši adresu.
```
