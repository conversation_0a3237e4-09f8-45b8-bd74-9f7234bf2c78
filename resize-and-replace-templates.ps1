Add-Type -AssemblyName System.Drawing

# Define paths
$sourceFolder = "new-templates"
$targetFolder = "public\templates\smlouva2"
$compressedTargetFolder = "public\templates\compressed\smlouva2"

# Target dimensions (to match current smlouva2)
$originalWidth = 1488
$originalHeight = 2105
$compressedWidth = 1400
$compressedHeight = 1980

Write-Host "=== RESIZING AND REPLACING SMLOUVA2 TEMPLATES ==="
Write-Host "Source folder: $sourceFolder"
Write-Host "Target dimensions (original): ${originalWidth}x${originalHeight}"
Write-Host "Target dimensions (compressed): ${compressedWidth}x${compressedHeight}"
Write-Host ""

# Create backup of current smlouva2 files
$backupFolder = "public\templates\smlouva2_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
Write-Host "Creating backup at: $backupFolder"
Copy-Item -Path $targetFolder -Destination $backupFolder -Recurse

$compressedBackupFolder = "public\templates\compressed\smlouva2_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
Write-Host "Creating compressed backup at: $compressedBackupFolder"
Copy-Item -Path $compressedTargetFolder -Destination $compressedBackupFolder -Recurse

Write-Host ""

# Function to resize image
function Resize-Image {
    param(
        [string]$SourcePath,
        [string]$DestinationPath,
        [int]$Width,
        [int]$Height
    )
    
    $sourceImage = [System.Drawing.Image]::FromFile($SourcePath)
    $destImage = New-Object System.Drawing.Bitmap($Width, $Height)
    $graphics = [System.Drawing.Graphics]::FromImage($destImage)
    
    # Set high quality settings
    $graphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::HighQualityBicubic
    $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::HighQuality
    $graphics.PixelOffsetMode = [System.Drawing.Drawing2D.PixelOffsetMode]::HighQuality
    $graphics.CompositingQuality = [System.Drawing.Drawing2D.CompositingQuality]::HighQuality
    
    # Draw resized image
    $graphics.DrawImage($sourceImage, 0, 0, $Width, $Height)
    
    # Save with high quality JPEG settings
    $encoder = [System.Drawing.Imaging.ImageCodecInfo]::GetImageEncoders() | Where-Object { $_.MimeType -eq "image/jpeg" }
    $encoderParams = New-Object System.Drawing.Imaging.EncoderParameters(1)
    $encoderParams.Param[0] = New-Object System.Drawing.Imaging.EncoderParameter([System.Drawing.Imaging.Encoder]::Quality, 95L)
    
    $destImage.Save($DestinationPath, $encoder, $encoderParams)
    
    # Cleanup
    $graphics.Dispose()
    $destImage.Dispose()
    $sourceImage.Dispose()
}

# Process each file (0.jpg through 5.jpg)
for ($i = 0; $i -lt 6; $i++) {
    $sourceFile = Join-Path $sourceFolder "$i.jpg"
    $targetFile = Join-Path $targetFolder "$i.jpg"
    $compressedTargetFile = Join-Path $compressedTargetFolder "$i.jpg"
    
    if (Test-Path $sourceFile) {
        Write-Host "Processing $i.jpg..."
        
        # Resize for original folder (1488x2105)
        Write-Host "  - Resizing to ${originalWidth}x${originalHeight} for original folder"
        Resize-Image -SourcePath $sourceFile -DestinationPath $targetFile -Width $originalWidth -Height $originalHeight
        
        # Resize for compressed folder (1400x1980)
        Write-Host "  - Resizing to ${compressedWidth}x${compressedHeight} for compressed folder"
        Resize-Image -SourcePath $sourceFile -DestinationPath $compressedTargetFile -Width $compressedWidth -Height $compressedHeight
        
        Write-Host "  - Completed $i.jpg"
    } else {
        Write-Host "  - WARNING: Source file $sourceFile not found!"
    }
}

Write-Host ""
Write-Host "=== REPLACEMENT COMPLETED ==="
Write-Host "Original smlouva2 files backed up to: $backupFolder"
Write-Host "Compressed smlouva2 files backed up to: $compressedBackupFolder"
Write-Host "New template files have been resized and placed in smlouva2 folders"
Write-Host ""
Write-Host "The positioning system should now work correctly with the new templates!"

# Clean up temp file
if (Test-Path "temp-check.jpg") {
    Remove-Item "temp-check.jpg"
}
