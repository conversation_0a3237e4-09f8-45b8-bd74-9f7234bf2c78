const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const { ensureAuth, checkRole } = require('../middleware/auth');
const { admin, db } = require('../firebase-admin');

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 1024 * 1024, // 1MB
  },
  fileFilter: (req, file, cb) => {
    // Accept only PNG files
    if (file.mimetype === 'image/png') {
      cb(null, true);
    } else {
      cb(new Error('Only PNG files are allowed'), false);
    }
  }
});

// Get logo settings
router.get('/get', ensureAuth, checkRole(['editor', 'admin']), async (req, res) => {
  console.log('GET /logo-settings endpoint called');
  try {
    const userId = req.user.id;
    console.log(`Fetching logo settings for user ${userId}...`);

    // Get user-specific logo settings from Firestore
    const settingsDoc = await db.collection('settings').doc('logos').collection('users').doc(userId).get();

    if (!settingsDoc.exists) {
      console.log(`No logo settings found for user ${userId}, returning defaults`);
      // Default settings if not found
      return res.json({
        enableLogo: true,
        logoUrl: null
      });
    }

    const settings = settingsDoc.data();
    console.log(`Logo settings found for user ${userId}:`, settings);

    // Check if the logo file exists if we have a local path
    if (settings.logoUrl && settings.logoUrl.startsWith('/uploads/')) {
      const fs = require('fs');
      const path = require('path');

      // Try different paths to find the logo file
      const possiblePaths = [
        path.join(__dirname, '../public', settings.logoUrl),
        path.join(__dirname, '../', settings.logoUrl),
        path.join(process.cwd(), 'public', settings.logoUrl),
        path.join(process.cwd(), settings.logoUrl.substring(1)) // Remove leading slash
      ];

      let fileFound = false;
      for (const logoPath of possiblePaths) {
        console.log('Checking path:', logoPath);
        if (fs.existsSync(logoPath)) {
          console.log('Logo file exists at path:', logoPath);
          fileFound = true;
          break;
        }
      }

      if (!fileFound) {
        console.log('Logo file not found in any of the checked paths');
        // Don't reset the URL, just log the warning
      }
    }

    res.json(settings);
  } catch (error) {
    console.error('Error fetching logo settings:', error);
    res.status(500).json({ message: 'Failed to fetch logo settings' });
  }
});

// Update logo settings
router.post('/upload', ensureAuth, checkRole(['editor', 'admin']), upload.single('logo'), async (req, res) => {
  try {
    const userId = req.user.id;
    const enableLogo = req.body.enableLogo === 'true';

    // Get current settings for this user
    const settingsRef = db.collection('settings').doc('logos').collection('users').doc(userId);
    const settingsDoc = await settingsRef.get();
    const currentSettings = settingsDoc.exists ? settingsDoc.data() : {};

    // Update settings object
    const settings = {
      ...currentSettings,
      enableLogo,
      userId: req.user.id,
      userDisplayName: req.user.displayName,
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    };

    // If a new logo was uploaded
    if (req.file) {
      try {
        // For now, we'll save the logo to the local filesystem instead of Firebase Storage
        // This is a temporary solution until Firebase Storage is properly configured
        const fs = require('fs');
        const path = require('path');

        // Create user-specific logos directory if it doesn't exist
        const userLogoDir = path.join(__dirname, `../public/uploads/logos/${userId}`);
        if (!fs.existsSync(userLogoDir)) {
          fs.mkdirSync(userLogoDir, { recursive: true });
        }

        // Generate a unique filename
        const fileName = `logo_${Date.now()}.png`;
        const filePath = path.join(userLogoDir, fileName);

        // Save the file
        fs.writeFileSync(filePath, req.file.buffer);

        // Generate a URL for the logo
        const logoUrl = `/uploads/logos/${userId}/${fileName}`;

        // Save the URL in settings
        settings.logoUrl = logoUrl;
        settings.logoPath = filePath;

        // Save settings to Firestore
        await settingsRef.set(settings, { merge: true });

        res.json({ success: true, message: 'Logo settings updated successfully' });
      } catch (error) {
        console.error('Error saving logo file:', error);
        res.status(500).json({ message: 'Failed to save logo file' });
      }
    } else {
      // No new logo, just update settings
      await settingsRef.set(settings, { merge: true });
      res.json({ success: true, message: 'Logo settings updated successfully' });
    }
  } catch (error) {
    console.error('Error updating logo settings:', error);
    res.status(500).json({ message: 'Failed to update logo settings' });
  }
});

module.exports = router;
